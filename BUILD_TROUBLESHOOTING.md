# Build Troubleshooting Guide

## 🚨 Current Issue: SwiftEmitModule Failed

### Error Description
`Command SwiftEmitModule failed with a nonzero exit code`

This is a generic Swift compilation error that can have multiple causes.

---

## 🔧 Step-by-Step Solutions

### Solution 1: Clean Build Environment
```bash
# In Terminal:
cd /Users/<USER>/Desktop/TaskPlus

# Clean Xcode build cache
rm -rf ~/Library/Developer/Xcode/DerivedData

# Clean project build folder
# In Xcode: Product → Clean Build Folder (⌘+Shift+K)
```

### Solution 2: Verify Package Dependencies
1. Open Xcode
2. Go to **File → Package Dependencies**
3. Verify `supabase-swift` is listed
4. If missing, add it:
   - **File → Add Package Dependencies**
   - URL: `https://github.com/supabase/supabase-swift.git`
   - Version: `2.0.0` or later

### Solution 3: Check Target Configuration
1. Select **TaskPlus** project in navigator
2. Select **TaskPlus** target
3. Verify settings:
   - **iOS Deployment Target**: 16.0+
   - **Swift Language Version**: Swift 5
   - **Build Settings → Architectures**: Standard architectures

### Solution 4: Verify Import Statements
All files should have correct imports:
```swift
// SupabaseManager.swift
import Foundation
import Supabase

// Other files
import Foundation
import SwiftUI
```

### Solution 5: Check for Circular Dependencies
Ensure no circular imports between:
- AuthenticationManager ↔ SupabaseManager
- DataManager ↔ SupabaseManager
- Views ↔ Managers

---

## 🧪 Build Test Verification

Run the build test to verify all components:

1. Open `TaskPlus/Utils/BuildTest.swift`
2. Add this to any view temporarily:
```swift
.onAppear {
    let _ = BuildTest.runAllTests()
}
```

Expected output:
```
🧪 Running build tests...
✅ All managers initialized successfully
✅ All models created successfully
✅ Design system accessible
✅ Supabase configuration is valid
🎉 All build tests passed!
```

---

## 📋 Checklist Before Building

- [ ] Xcode build cache cleaned
- [ ] DerivedData folder deleted
- [ ] Supabase package dependency added
- [ ] iOS Deployment Target ≥ 16.0
- [ ] Swift Language Version = Swift 5
- [ ] No syntax errors in any file
- [ ] All imports are correct
- [ ] No circular dependencies

---

## 🆘 If All Else Fails

### Nuclear Option: Fresh Project Setup
1. Create new iOS project in Xcode
2. Copy all `.swift` files from current project
3. Add Supabase package dependency
4. Configure project settings
5. Test build

### Alternative: Incremental Build Test
1. Comment out all Supabase-related code
2. Build successfully
3. Gradually uncomment sections
4. Identify problematic code

---

## 📞 Common Error Patterns

### Pattern 1: Missing Package
```
error: No such module 'Supabase'
```
**Solution**: Add Supabase package dependency

### Pattern 2: Version Conflict
```
error: Package resolution failed
```
**Solution**: Update package versions or reset package cache

### Pattern 3: Circular Import
```
error: Circular dependency between modules
```
**Solution**: Restructure imports and dependencies

### Pattern 4: Swift Version Mismatch
```
error: Unsupported Swift version
```
**Solution**: Update Swift Language Version in build settings

---

## 🎯 Quick Fixes to Try First

1. **⌘ + Shift + K** (Clean Build Folder)
2. **⌘ + R** (Build and Run)
3. Restart Xcode
4. Restart Mac (if desperate 😅)

---

## 📈 Success Indicators

When build succeeds, you should see:
- ✅ No compilation errors
- ✅ App launches successfully
- ✅ Authentication screens appear
- ✅ Supabase connection works
- ✅ All managers initialize properly

---

*Last updated: December 14, 2024*
*TaskMate Phase 2.5 - Supabase Integration*
