// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXContainerItemProxy section */
		46A1C5BA2DF7A54E00552ABD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 46A1C5A12DF7A54C00552ABD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 46A1C5A82DF7A54C00552ABD;
			remoteInfo = TaskPlus;
		};
		46A1C5C42DF7A54E00552ABD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 46A1C5A12DF7A54C00552ABD /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 46A1C5A82DF7A54C00552ABD;
			remoteInfo = TaskPlus;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		46A1C5A92DF7A54C00552ABD /* TaskPlus.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = TaskPlus.app; sourceTree = BUILT_PRODUCTS_DIR; };
		46A1C5B92DF7A54E00552ABD /* TaskPlusTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TaskPlusTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		46A1C5C32DF7A54E00552ABD /* TaskPlusUITests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = TaskPlusUITests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		46A1C5AB2DF7A54C00552ABD /* TaskPlus */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TaskPlus;
			sourceTree = "<group>";
		};
		46A1C5BC2DF7A54E00552ABD /* TaskPlusTests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TaskPlusTests;
			sourceTree = "<group>";
		};
		46A1C5C62DF7A54E00552ABD /* TaskPlusUITests */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			path = TaskPlusUITests;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		46A1C5A62DF7A54C00552ABD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		46A1C5B62DF7A54E00552ABD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		46A1C5C02DF7A54E00552ABD /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		46A1C5A02DF7A54C00552ABD = {
			isa = PBXGroup;
			children = (
				46A1C5AB2DF7A54C00552ABD /* TaskPlus */,
				46A1C5BC2DF7A54E00552ABD /* TaskPlusTests */,
				46A1C5C62DF7A54E00552ABD /* TaskPlusUITests */,
				46A1C5AA2DF7A54C00552ABD /* Products */,
			);
			sourceTree = "<group>";
		};
		46A1C5AA2DF7A54C00552ABD /* Products */ = {
			isa = PBXGroup;
			children = (
				46A1C5A92DF7A54C00552ABD /* TaskPlus.app */,
				46A1C5B92DF7A54E00552ABD /* TaskPlusTests.xctest */,
				46A1C5C32DF7A54E00552ABD /* TaskPlusUITests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		46A1C5A82DF7A54C00552ABD /* TaskPlus */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 46A1C5CD2DF7A54E00552ABD /* Build configuration list for PBXNativeTarget "TaskPlus" */;
			buildPhases = (
				46A1C5A52DF7A54C00552ABD /* Sources */,
				46A1C5A62DF7A54C00552ABD /* Frameworks */,
				46A1C5A72DF7A54C00552ABD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				46A1C5AB2DF7A54C00552ABD /* TaskPlus */,
			);
			name = TaskPlus;
			packageProductDependencies = (
			);
			productName = TaskPlus;
			productReference = 46A1C5A92DF7A54C00552ABD /* TaskPlus.app */;
			productType = "com.apple.product-type.application";
		};
		46A1C5B82DF7A54E00552ABD /* TaskPlusTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 46A1C5D02DF7A54E00552ABD /* Build configuration list for PBXNativeTarget "TaskPlusTests" */;
			buildPhases = (
				46A1C5B52DF7A54E00552ABD /* Sources */,
				46A1C5B62DF7A54E00552ABD /* Frameworks */,
				46A1C5B72DF7A54E00552ABD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				46A1C5BB2DF7A54E00552ABD /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				46A1C5BC2DF7A54E00552ABD /* TaskPlusTests */,
			);
			name = TaskPlusTests;
			packageProductDependencies = (
			);
			productName = TaskPlusTests;
			productReference = 46A1C5B92DF7A54E00552ABD /* TaskPlusTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		46A1C5C22DF7A54E00552ABD /* TaskPlusUITests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 46A1C5D32DF7A54E00552ABD /* Build configuration list for PBXNativeTarget "TaskPlusUITests" */;
			buildPhases = (
				46A1C5BF2DF7A54E00552ABD /* Sources */,
				46A1C5C02DF7A54E00552ABD /* Frameworks */,
				46A1C5C12DF7A54E00552ABD /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
				46A1C5C52DF7A54E00552ABD /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				46A1C5C62DF7A54E00552ABD /* TaskPlusUITests */,
			);
			name = TaskPlusUITests;
			packageProductDependencies = (
			);
			productName = TaskPlusUITests;
			productReference = 46A1C5C32DF7A54E00552ABD /* TaskPlusUITests.xctest */;
			productType = "com.apple.product-type.bundle.ui-testing";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		46A1C5A12DF7A54C00552ABD /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1620;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					46A1C5A82DF7A54C00552ABD = {
						CreatedOnToolsVersion = 16.2;
					};
					46A1C5B82DF7A54E00552ABD = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 46A1C5A82DF7A54C00552ABD;
					};
					46A1C5C22DF7A54E00552ABD = {
						CreatedOnToolsVersion = 16.2;
						TestTargetID = 46A1C5A82DF7A54C00552ABD;
					};
				};
			};
			buildConfigurationList = 46A1C5A42DF7A54C00552ABD /* Build configuration list for PBXProject "TaskPlus" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 46A1C5A02DF7A54C00552ABD;
			minimizedProjectReferenceProxies = 1;
			preferredProjectObjectVersion = 77;
			productRefGroup = 46A1C5AA2DF7A54C00552ABD /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				46A1C5A82DF7A54C00552ABD /* TaskPlus */,
				46A1C5B82DF7A54E00552ABD /* TaskPlusTests */,
				46A1C5C22DF7A54E00552ABD /* TaskPlusUITests */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		46A1C5A72DF7A54C00552ABD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		46A1C5B72DF7A54E00552ABD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		46A1C5C12DF7A54E00552ABD /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		46A1C5A52DF7A54C00552ABD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		46A1C5B52DF7A54E00552ABD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		46A1C5BF2DF7A54E00552ABD /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		46A1C5BB2DF7A54E00552ABD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 46A1C5A82DF7A54C00552ABD /* TaskPlus */;
			targetProxy = 46A1C5BA2DF7A54E00552ABD /* PBXContainerItemProxy */;
		};
		46A1C5C52DF7A54E00552ABD /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 46A1C5A82DF7A54C00552ABD /* TaskPlus */;
			targetProxy = 46A1C5C42DF7A54E00552ABD /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		46A1C5CB2DF7A54E00552ABD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		46A1C5CC2DF7A54E00552ABD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		46A1C5CE2DF7A54E00552ABD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"TaskPlus/Preview Content\"";
				DEVELOPMENT_TEAM = 7B6KA3H543;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Task.TaskPlus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		46A1C5CF2DF7A54E00552ABD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "\"TaskPlus/Preview Content\"";
				DEVELOPMENT_TEAM = 7B6KA3H543;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Task.TaskPlus;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		46A1C5D12DF7A54E00552ABD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7B6KA3H543;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Task.TaskPlusTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TaskPlus.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TaskPlus";
			};
			name = Debug;
		};
		46A1C5D22DF7A54E00552ABD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				BUNDLE_LOADER = "$(TEST_HOST)";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7B6KA3H543;
				GENERATE_INFOPLIST_FILE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.2;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Task.TaskPlusTests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/TaskPlus.app/$(BUNDLE_EXECUTABLE_FOLDER_PATH)/TaskPlus";
			};
			name = Release;
		};
		46A1C5D42DF7A54E00552ABD /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7B6KA3H543;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Task.TaskPlusUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TaskPlus;
			};
			name = Debug;
		};
		46A1C5D52DF7A54E00552ABD /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 7B6KA3H543;
				GENERATE_INFOPLIST_FILE = YES;
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.Task.TaskPlusUITests;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = NO;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
				TEST_TARGET_NAME = TaskPlus;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		46A1C5A42DF7A54C00552ABD /* Build configuration list for PBXProject "TaskPlus" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46A1C5CB2DF7A54E00552ABD /* Debug */,
				46A1C5CC2DF7A54E00552ABD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		46A1C5CD2DF7A54E00552ABD /* Build configuration list for PBXNativeTarget "TaskPlus" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46A1C5CE2DF7A54E00552ABD /* Debug */,
				46A1C5CF2DF7A54E00552ABD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		46A1C5D02DF7A54E00552ABD /* Build configuration list for PBXNativeTarget "TaskPlusTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46A1C5D12DF7A54E00552ABD /* Debug */,
				46A1C5D22DF7A54E00552ABD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		46A1C5D32DF7A54E00552ABD /* Build configuration list for PBXNativeTarget "TaskPlusUITests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				46A1C5D42DF7A54E00552ABD /* Debug */,
				46A1C5D52DF7A54E00552ABD /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 46A1C5A12DF7A54C00552ABD /* Project object */;
}
