//
//  DataManager.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import SwiftUI

// MARK: - Data Manager
@MainActor
class DataManager: ObservableObject {
    static let shared = DataManager()
    
    // MARK: - Managers
    private let supabaseManager = SupabaseManager.shared

    // MARK: - Published Properties
    @Published var currentUser: User?
    @Published var tasks: [Task] = []
    @Published var groups: [Group] = []
    @Published var friends: [User] = []

    // MARK: - Loading States
    @Published var isLoading = false
    @Published var errorMessage: String?

    // MARK: - Sync Status
    @Published var isSyncing = false
    @Published var lastSyncDate: Date?
    @Published var isOfflineMode = false

    private init() {
        // Load minimal sample data for offline mode
        loadMinimalSampleData()
        setupSyncObserver()
    }

    // MARK: - Sync Management
    private func setupSyncObserver() {
        // Monitor network connectivity and sync when available
        _Concurrency.Task {
            await syncDataIfNeeded()
        }
    }

    func syncDataIfNeeded() async {
        guard !isSyncing else { return }

        isSyncing = true

        do {
            // Try to sync with Supabase
            if supabaseManager.isConnected {
                await syncWithSupabase()
                isOfflineMode = false
            } else {
                isOfflineMode = true
            }
        } catch {
            print("Sync failed: \(error)")
            isOfflineMode = true
        }

        isSyncing = false
        lastSyncDate = Date()
    }

    private func syncWithSupabase() async {
        do {
            // Only sync if user is authenticated
            guard supabaseManager.currentUser != nil else {
                print("No authenticated user - skipping sync")
                return
            }

            // Sync tasks from Supabase for current user
            let cloudTasks = try await supabaseManager.fetchTasks()

            // Replace local tasks with cloud tasks (cloud is source of truth)
            await MainActor.run {
                self.tasks = cloudTasks
                print("✅ Synced \(cloudTasks.count) tasks from Supabase")
            }

        } catch {
            print("❌ Failed to sync with Supabase: \(error)")
            // Keep local tasks if sync fails
        }
    }
    
    // MARK: - Minimal Data Loading (for offline mode)
    private func loadMinimalSampleData() {
        // Only load sample groups and friends for offline mode
        // Tasks will be loaded from Supabase
        groups = Group.sampleGroups
        friends = User.sampleUsers

        // Initialize empty tasks array - will be populated from Supabase
        tasks = []
    }
    
    // MARK: - User Management
    func updateUser(_ user: User) {
        currentUser = user
        // Sync tasks when user changes
        _Concurrency.Task {
            await syncDataIfNeeded()
        }
    }

    func setCurrentUser(_ user: User?) {
        print("🔄 DataManager.setCurrentUser() called")
        print("  - User: \(user?.username ?? "NIL")")

        currentUser = user
        if user != nil {
            print("✅ User logged in - syncing tasks...")
            // User logged in - sync their tasks
            _Concurrency.Task {
                await syncDataIfNeeded()
            }
        } else {
            print("ℹ️ User logged out - clearing tasks")
            // User logged out - clear tasks
            tasks = []
        }
    }
    
    func updateUserStats(taskCompleted: Bool = false, groupJoined: Bool = false, groupCreated: Bool = false, friendAdded: Bool = false, messageSent: Bool = false, messageReceived: Bool = false) {
        guard var user = currentUser else { return }
        user.updateStats(
            taskCompleted: taskCompleted,
            groupJoined: groupJoined,
            groupCreated: groupCreated,
            friendAdded: friendAdded,
            messageSent: messageSent,
            messageReceived: messageReceived
        )
        currentUser = user
    }
    
    // MARK: - Task Management (Hybrid: Local + Cloud)
    func addTask(_ task: Task) async {
        // 1. Add to local storage immediately (for instant UI update)
        tasks.append(task)
        updateUserStats(taskCompleted: false)

        // 2. Sync to cloud in background
        if !isOfflineMode {
            _Concurrency.Task {
                do {
                    let _ = try await supabaseManager.createTask(task)
                } catch {
                    print("Failed to sync task to cloud: \(error)")
                }
            }
        }
    }

    func updateTask(_ task: Task) async {
        // 1. Update local storage immediately
        if let index = tasks.firstIndex(where: { $0.id == task.id }) {
            let wasCompleted = tasks[index].status == .completed
            let isNowCompleted = task.status == .completed

            tasks[index] = task

            // Update stats if task was just completed
            if !wasCompleted && isNowCompleted {
                updateUserStats(taskCompleted: true)
            }
        }

        // 2. Sync to cloud in background
        if !isOfflineMode {
            _Concurrency.Task {
                do {
                    try await supabaseManager.updateTask(task)
                } catch {
                    print("Failed to sync task update to cloud: \(error)")
                }
            }
        }
    }

    func deleteTask(_ task: Task) async {
        // 1. Remove from local storage immediately
        tasks.removeAll { $0.id == task.id }

        // 2. Delete from cloud in background
        if !isOfflineMode {
            _Concurrency.Task {
                do {
                    try await supabaseManager.deleteTask(task.id)
                } catch {
                    print("Failed to delete task from cloud: \(error)")
                }
            }
        }
    }
    
    func toggleTaskCompletion(_ task: Task) async {
        var updatedTask = task
        updatedTask.status = task.status == .completed ? .inProgress : .completed
        updatedTask.completedAt = updatedTask.status == .completed ? Date() : nil
        await updateTask(updatedTask)
    }

    // MARK: - Manual Sync
    func forceSyncWithCloud() async {
        await syncDataIfNeeded()
    }

    // MARK: - Offline Mode Management
    func enableOfflineMode() {
        isOfflineMode = true
    }

    func disableOfflineMode() {
        isOfflineMode = false
        _Concurrency.Task {
            await syncDataIfNeeded()
        }
    }
    
    // MARK: - Group Management
    func addGroup(_ group: Group) {
        groups.append(group)
        updateUserStats(groupCreated: true)
    }
    
    func updateGroup(_ group: Group) {
        if let index = groups.firstIndex(where: { $0.id == group.id }) {
            groups[index] = group
        }
    }
    
    func deleteGroup(_ group: Group) {
        groups.removeAll { $0.id == group.id }
    }
    
    func joinGroup(_ group: Group) {
        // In a real app, this would send a join request
        updateUserStats(groupJoined: true)
    }
    
    // MARK: - Friend Management
    func addFriend(_ user: User) {
        if !friends.contains(where: { $0.id == user.id }) {
            friends.append(user)
            updateUserStats(friendAdded: true)
        }
    }
    
    func removeFriend(_ user: User) {
        friends.removeAll { $0.id == user.id }
    }
    
    // MARK: - Search Functions
    func searchTasks(_ query: String) -> [Task] {
        guard !query.isEmpty else { return tasks }
        return tasks.filter { task in
            task.title.localizedCaseInsensitiveContains(query) ||
            (task.description?.localizedCaseInsensitiveContains(query) ?? false)
        }
    }
    
    func searchGroups(_ query: String) -> [Group] {
        guard !query.isEmpty else { return groups }
        return groups.filter { group in
            group.name.localizedCaseInsensitiveContains(query) ||
            (group.description?.localizedCaseInsensitiveContains(query) ?? false)
        }
    }
    
    func searchFriends(_ query: String) -> [User] {
        guard !query.isEmpty else { return friends }
        return friends.filter { user in
            user.matchesSearch(query)
        }
    }
    
    // MARK: - Filter Functions
    func getTasksByStatus(_ status: Task.TaskStatus) -> [Task] {
        return tasks.filter { $0.status == status }
    }
    
    func getTasksByPriority(_ priority: Task.Priority) -> [Task] {
        return tasks.filter { $0.priority == priority }
    }
    
    func getTasksByType(_ type: Task.TaskType) -> [Task] {
        return tasks.filter { $0.taskType == type }
    }
    
    func getUpcomingTasks() -> [Task] {
        let calendar = Calendar.current
        let today = Date()
        let nextWeek = calendar.date(byAdding: .weekOfYear, value: 1, to: today) ?? today
        
        return tasks.filter { task in
            guard let dueDate = task.dueDate else { return false }
            return dueDate >= today && dueDate <= nextWeek && task.status != .completed
        }
    }
    
    func getOverdueTasks() -> [Task] {
        let today = Date()
        return tasks.filter { task in
            guard let dueDate = task.dueDate else { return false }
            return dueDate < today && task.status != .completed
        }
    }
    
    // MARK: - Statistics
    func getProductivityStats() -> (completed: Int, pending: Int, overdue: Int) {
        let completed = tasks.filter { $0.status == .completed }.count
        let pending = tasks.filter { $0.status == .inProgress }.count
        let overdue = getOverdueTasks().count
        
        return (completed: completed, pending: pending, overdue: overdue)
    }
    
    // MARK: - Error Handling
    func clearError() {
        errorMessage = nil
    }
    
    private func setError(_ message: String) {
        errorMessage = message
    }
}

// MARK: - Sample Data Extensions
extension Task {
    static let sampleTasks: [Task] = [
        Task(
            title: "Complete iOS App Design",
            description: "Finish the UI/UX design for the TaskMate application",
            dueDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()),
            priority: .high,
            createdByUserId: UUID(),
            taskType: .personal
        ),
        Task(
            title: "Team Meeting Preparation",
            description: "Prepare agenda and materials for the weekly team meeting",
            dueDate: Calendar.current.date(byAdding: .day, value: 1, to: Date()),
            priority: .medium,
            createdByUserId: UUID(),
            taskType: .group
        ),
        {
            var task = Task(
                title: "Code Review",
                description: "Review pull requests from team members",
                priority: .medium,
                createdByUserId: UUID(),
                taskType: .group
            )
            task.status = .completed
            task.completedAt = Calendar.current.date(byAdding: .day, value: -1, to: Date())
            return task
        }(),
        Task(
            title: "Grocery Shopping",
            description: "Buy groceries for the week",
            dueDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()),
            priority: .low,
            createdByUserId: UUID(),
            taskType: .personal
        ),
        Task(
            title: "Project Documentation",
            description: "Update project documentation and README files",
            dueDate: Calendar.current.date(byAdding: .day, value: -1, to: Date()), // Overdue
            priority: .high,
            createdByUserId: UUID(),
            taskType: .group
        )
    ]
}

extension Group {
    static let sampleGroups: [Group] = [
        Group(
            name: "iOS Development Team",
            description: "Collaborative group for iOS app development projects",
            ownerId: UUID()
        ),
        Group(
            name: "Fitness Challenge",
            description: "Monthly fitness and wellness challenge group",
            ownerId: UUID()
        ),
        Group(
            name: "Book Club",
            description: "Monthly book reading and discussion group",
            ownerId: UUID()
        )
    ]
}
