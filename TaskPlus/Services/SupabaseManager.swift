//
//  SupabaseManager.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import Supabase

// MARK: - Supabase Manager
@MainActor
class SupabaseManager: ObservableObject {
    static let shared = SupabaseManager()
    
    // MARK: - Supabase Client
    private let supabase: SupabaseClient
    
    // MARK: - Published Properties
    @Published var isConnected = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private init() {
        // Initialize Supabase client
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://bvqwlkudghfrrugjbvbh.supabase.co")!,
            supabaseKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ2cXdsa3VkZ2hmcnJ1Z2pidmJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk1MTYwMjIsImV4cCI6MjA2NTA5MjAyMn0.nF85xHTVAqcNevob6O0LF5ErBOsiKGHN5e16u53ZApw"
        )
        
        checkConnection()
    }
    
    // MARK: - Connection Management
    private func checkConnection() {
        _Concurrency.Task {
            do {
                // Test connection with a simple query
                let _: [Profile] = try await supabase
                    .from("profiles")
                    .select("id")
                    .limit(1)
                    .execute()
                    .value
                
                await MainActor.run {
                    isConnected = true
                }
            } catch {
                await MainActor.run {
                    isConnected = false
                    errorMessage = "Failed to connect to Supabase: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // MARK: - Authentication Status
    var isAuthenticated: Bool {
        return supabase.auth.currentUser != nil
    }

    var currentUserId: UUID? {
        return supabase.auth.currentUser?.id
    }

    // MARK: - Authentication
    func signUp(email: String, password: String, userData: [String: Any]) async throws -> User {
        isLoading = true
        errorMessage = nil

        do {
            // Use simple signup without metadata for now
            let authResponse = try await supabase.auth.signUp(
                email: email,
                password: password
            )
            
            let _ = authResponse.user

            // Create local user object
            let localUser = User(
                username: userData["username"] as? String ?? "",
                displayName: userData["display_name"] as? String ?? "",
                email: email
            )

            // Note: Profile will be created automatically by database trigger
            // We could update it here with additional data if needed

            currentUser = localUser
            isLoading = false
            return localUser
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    func signIn(email: String, password: String) async throws -> User {
        isLoading = true
        errorMessage = nil
        
        do {
            let authResponse = try await supabase.auth.signIn(
                email: email,
                password: password
            )
            
            let user = authResponse.user
            
            // Fetch user profile
            let profile: Profile = try await supabase
                .from("profiles")
                .select()
                .eq("id", value: user.id)
                .single()
                .execute()
                .value
            
            let localUser = User(
                username: profile.username,
                displayName: profile.displayName,
                email: profile.email,
                bio: profile.bio
            )
            
            currentUser = localUser
            isLoading = false
            return localUser
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    func signOut() async throws {
        try await supabase.auth.signOut()
        currentUser = nil
    }

    // MARK: - Session Management
    func restoreSession() async {
        print("🔄 restoreSession() called")
        do {
            // Check if there's an existing session
            if let session = supabase.auth.currentSession {
                print("✅ Found existing Supabase session for user: \(session.user.id)")
                print("  - Session valid until: \(session.expiresAt)")
                print("  - User email: \(session.user.email ?? "unknown")")

                // Fetch user profile to restore currentUser
                let profile: Profile = try await supabase
                    .from("profiles")
                    .select()
                    .eq("id", value: session.user.id)
                    .single()
                    .execute()
                    .value

                let localUser = User(
                    username: profile.username,
                    displayName: profile.displayName,
                    email: profile.email,
                    bio: profile.bio
                )

                currentUser = localUser
                print("✅ Session restored successfully for user: \(localUser.username)")
            } else {
                print("ℹ️ No existing Supabase session found")
            }
        } catch {
            print("❌ Failed to restore session: \(error)")
        }
    }

    // MARK: - Profile Management
    private func ensureProfileExists(for userId: UUID) async {
        do {
            // Check if profile exists
            let _: Profile = try await supabase
                .from("profiles")
                .select()
                .eq("id", value: userId)
                .single()
                .execute()
                .value

            print("✅ Profile exists for user: \(userId)")

        } catch {
            print("⚠️ Profile not found, creating one...")

            // Create profile if it doesn't exist
            do {
                guard let userEmail = supabase.auth.currentUser?.email else {
                    print("❌ Cannot create profile: no email")
                    return
                }

                let newProfile = Profile(
                    id: userId,
                    username: "user_\(userId.uuidString.prefix(8))",
                    displayName: userEmail.components(separatedBy: "@").first ?? "User",
                    email: userEmail,
                    bio: nil
                )

                let _: Profile = try await supabase
                    .from("profiles")
                    .insert(newProfile)
                    .select()
                    .single()
                    .execute()
                    .value

                print("✅ Profile created successfully for user: \(userId)")

            } catch {
                print("❌ Failed to create profile: \(error)")
            }
        }
    }
    
    // MARK: - Profile Management
    func updateProfile(_ user: User) async throws {
        guard let currentUserId = supabase.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }
        
        let profileUpdate = ProfileUpdate(
            username: user.username,
            displayName: user.displayName,
            bio: user.bio,
            avatarUrl: user.avatarURL,
            updatedAt: Date()
        )
        
        try await supabase
            .from("profiles")
            .update(profileUpdate)
            .eq("id", value: currentUserId)
            .execute()
        
        currentUser = user
    }
    
    // MARK: - Task Management
    func createTask(_ task: Task) async throws -> Task {
        print("🔍 createTask() called for: \(task.title)")
        print("🔍 Current session exists: \(supabase.auth.currentSession != nil)")
        print("🔍 Current user ID: \(supabase.auth.currentUser?.id.uuidString ?? "NIL")")

        guard let authUserId = supabase.auth.currentUser?.id else {
            print("❌ createTask failed: no authenticated user")
            throw SupabaseError.notAuthenticated
        }

        print("✅ createTask proceeding with Auth UUID: \(authUserId)")

        // Ensure profile exists before creating task
        await ensureProfileExists(for: authUserId)

        let taskInsert = TaskInsert(
            title: task.title,
            description: task.description,
            dueDate: task.dueDate,
            priority: task.priority.rawValue,
            status: task.status.rawValue,
            taskType: task.taskType.rawValue,
            createdBy: authUserId,
            groupId: task.groupId,
            subtasks: task.subtasks.map { SubtaskData(from: $0) },
            attachments: task.attachments.map { AttachmentData(from: $0) },
            location: task.location != nil ? LocationData(from: task.location!) : nil,
            reminderTime: task.reminderTime,
            isImportant: task.isImportant,
            difficulty: task.difficulty.rawValue,
            category: task.category != nil ? CategoryData(from: task.category!) : nil,
            tags: task.tags,
            estimatedDuration: task.estimatedDuration,
            recurrence: task.recurrence != nil ? RecurrenceData(from: task.recurrence!) : nil
        )
        
        let insertedTask: TaskResponse = try await supabase
            .from("tasks")
            .insert(taskInsert)
            .select()
            .single()
            .execute()
            .value

        print("✅ createTask completed successfully")
        print("  - Task ID: \(insertedTask.id)")
        print("  - Task Title: \(insertedTask.title)")
        print("  - Created By: \(insertedTask.createdBy)")

        return insertedTask.toTask()
    }
    
    func fetchTasks() async throws -> [Task] {
        print("🔍 fetchTasks() called")
        print("🔍 Current session exists: \(supabase.auth.currentSession != nil)")
        print("🔍 Current user ID: \(supabase.auth.currentUser?.id.uuidString ?? "NIL")")
        print("🔍 SupabaseManager currentUser: \(currentUser?.username ?? "NIL")")

        guard let currentUserId = supabase.auth.currentUser?.id else {
            print("❌ fetchTasks failed: no authenticated user")
            throw SupabaseError.notAuthenticated
        }

        print("✅ fetchTasks proceeding with user ID: \(currentUserId)")
        print("🔍 Querying tasks for user: \(currentUserId.uuidString)")
        
        // Use Auth UUID directly (should match profile.id after trigger fix)
        let taskResponses: [TaskResponse] = try await supabase
            .from("tasks")
            .select()
            .eq("created_by", value: currentUserId)
            .order("created_at", ascending: false)
            .execute()
            .value

        print("✅ fetchTasks completed: found \(taskResponses.count) tasks")
        for task in taskResponses {
            print("  - Task: \(task.title)")
        }

        return taskResponses.map { $0.toTask() }
    }
    
    func updateTask(_ task: Task) async throws {
        let taskUpdate = TaskUpdate(
            title: task.title,
            description: task.description,
            dueDate: task.dueDate,
            priority: task.priority.rawValue,
            status: task.status.rawValue,
            completedAt: task.completedAt,
            updatedAt: Date(),
            subtasks: task.subtasks.map { SubtaskData(from: $0) },
            attachments: task.attachments.map { AttachmentData(from: $0) },
            location: task.location != nil ? LocationData(from: task.location!) : nil,
            reminderTime: task.reminderTime,
            isImportant: task.isImportant,
            difficulty: task.difficulty.rawValue,
            category: task.category != nil ? CategoryData(from: task.category!) : nil,
            tags: task.tags,
            estimatedDuration: task.estimatedDuration,
            recurrence: task.recurrence != nil ? RecurrenceData(from: task.recurrence!) : nil
        )
        
        try await supabase
            .from("tasks")
            .update(taskUpdate)
            .eq("id", value: task.id)
            .execute()
    }
    
    func deleteTask(_ taskId: UUID) async throws {
        try await supabase
            .from("tasks")
            .delete()
            .eq("id", value: taskId)
            .execute()
    }
    
    // MARK: - Error Handling
    func clearError() {
        errorMessage = nil
    }
}

// MARK: - Supabase Errors
enum SupabaseError: LocalizedError {
    case notAuthenticated
    case authenticationFailed
    case networkError
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .notAuthenticated:
            return "User not authenticated"
        case .authenticationFailed:
            return "Authentication failed"
        case .networkError:
            return "Network connection error"
        case .unknownError:
            return "An unknown error occurred"
        }
    }
}

// MARK: - Supabase Data Models
struct Profile: Codable {
    let id: UUID
    let username: String
    let displayName: String
    let email: String
    let bio: String?
    let avatarUrl: String?
    let privacySetting: String
    let createdAt: Date
    let updatedAt: Date

    enum CodingKeys: String, CodingKey {
        case id, username, email, bio
        case displayName = "display_name"
        case avatarUrl = "avatar_url"
        case privacySetting = "privacy_setting"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct ProfileUpdate: Codable {
    let username: String
    let displayName: String
    let bio: String?
    let avatarUrl: String?
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case username, bio
        case displayName = "display_name"
        case avatarUrl = "avatar_url"
        case updatedAt = "updated_at"
    }
}

struct TaskInsert: Codable {
    let title: String
    let description: String?
    let dueDate: Date?
    let priority: String
    let status: String
    let taskType: String
    let createdBy: UUID
    let groupId: UUID?

    // Enhanced fields
    let subtasks: [SubtaskData]?
    let attachments: [AttachmentData]?
    let location: LocationData?
    let reminderTime: Date?
    let isImportant: Bool?
    let difficulty: String?
    let category: CategoryData?
    let tags: [String]?
    let estimatedDuration: TimeInterval?
    let recurrence: RecurrenceData?

    enum CodingKeys: String, CodingKey {
        case title, description, priority, status, subtasks, attachments, location, category, tags, recurrence
        case dueDate = "due_date"
        case taskType = "task_type"
        case createdBy = "created_by"
        case groupId = "group_id"
        case reminderTime = "reminder_time"
        case isImportant = "is_important"
        case difficulty
        case estimatedDuration = "estimated_duration"
    }
}

struct TaskResponse: Codable {
    let id: UUID
    let title: String
    let description: String?
    let dueDate: Date?
    let priority: String
    let status: String
    let taskType: String
    let createdBy: UUID
    let groupId: UUID?
    let completedAt: Date?
    let createdAt: Date
    let updatedAt: Date

    // Enhanced fields
    let subtasks: [SubtaskData]?
    let attachments: [AttachmentData]?
    let location: LocationData?
    let reminderTime: Date?
    let isImportant: Bool?
    let difficulty: String?
    let category: CategoryData?
    let tags: [String]?
    let estimatedDuration: TimeInterval?
    let recurrence: RecurrenceData?

    enum CodingKeys: String, CodingKey {
        case id, title, description, priority, status, subtasks, attachments, location, category, tags, recurrence
        case dueDate = "due_date"
        case taskType = "task_type"
        case createdBy = "created_by"
        case groupId = "group_id"
        case completedAt = "completed_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        case reminderTime = "reminder_time"
        case isImportant = "is_important"
        case difficulty
        case estimatedDuration = "estimated_duration"
    }
    
    func toTask() -> Task {
        var task = Task(
            id: id,
            title: title,
            description: description,
            dueDate: dueDate,
            priority: Task.Priority(rawValue: priority) ?? .medium,
            status: Task.TaskStatus(rawValue: status) ?? .inProgress,
            createdByUserId: createdBy,
            taskType: Task.TaskType(rawValue: taskType) ?? .personal,
            groupId: groupId,
            completedAt: completedAt,
            createdAt: createdAt,
            updatedAt: updatedAt
        )

        // Set enhanced properties
        task.subtasks = subtasks?.map { $0.toSubtask() } ?? []
        task.location = location?.toTaskLocation()
        task.reminderTime = reminderTime
        task.isImportant = isImportant ?? false
        task.difficulty = Task.Difficulty(rawValue: difficulty ?? "medium") ?? .medium
        task.category = category?.toTaskCategory()
        task.tags = tags ?? []
        task.estimatedDuration = estimatedDuration
        task.recurrence = recurrence?.toRecurrencePattern()

        // Convert attachments
        if let attachmentData = attachments {
            task.attachments = attachmentData.map { data in
                TaskAttachment(
                    name: data.name,
                    type: TaskAttachment.AttachmentType(rawValue: data.type) ?? .document,
                    url: data.url
                )
            }
        }

        return task
    }
}

struct TaskUpdate: Codable {
    let title: String
    let description: String?
    let dueDate: Date?
    let priority: String
    let status: String
    let completedAt: Date?
    let updatedAt: Date

    // Enhanced fields
    let subtasks: [SubtaskData]?
    let attachments: [AttachmentData]?
    let location: LocationData?
    let reminderTime: Date?
    let isImportant: Bool?
    let difficulty: String?
    let category: CategoryData?
    let tags: [String]?
    let estimatedDuration: TimeInterval?
    let recurrence: RecurrenceData?

    enum CodingKeys: String, CodingKey {
        case title, description, priority, status, subtasks, attachments, location, category, tags, recurrence
        case dueDate = "due_date"
        case completedAt = "completed_at"
        case updatedAt = "updated_at"
        case reminderTime = "reminder_time"
        case isImportant = "is_important"
        case difficulty
        case estimatedDuration = "estimated_duration"
    }
}

// MARK: - Enhanced Data Models for Supabase
struct SubtaskData: Codable {
    let id: UUID
    let title: String
    let isCompleted: Bool
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, title
        case isCompleted = "is_completed"
        case createdAt = "created_at"
    }

    init(from subtask: Subtask) {
        self.id = subtask.id
        self.title = subtask.title
        self.isCompleted = subtask.isCompleted
        self.createdAt = subtask.createdAt
    }

    func toSubtask() -> Subtask {
        var subtask = Subtask(title: title)
        subtask.isCompleted = isCompleted
        return subtask
    }
}

struct AttachmentData: Codable {
    let id: UUID
    let name: String
    let type: String
    let url: String?
    let localPath: String?
    let size: Int64?
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, type, url, size
        case localPath = "local_path"
        case createdAt = "created_at"
    }

    init(from attachment: TaskAttachment) {
        self.id = attachment.id
        self.name = attachment.name
        self.type = attachment.type.rawValue
        self.url = attachment.url
        self.localPath = attachment.localPath
        self.size = attachment.size
        self.createdAt = attachment.createdAt
    }
}

struct LocationData: Codable {
    let name: String
    let address: String?
    let latitude: Double?
    let longitude: Double?
    let radius: Double?

    init(from location: TaskLocation) {
        self.name = location.name
        self.address = location.address
        self.latitude = location.latitude
        self.longitude = location.longitude
        self.radius = location.radius
    }

    func toTaskLocation() -> TaskLocation {
        return TaskLocation(
            name: name,
            address: address,
            latitude: latitude,
            longitude: longitude,
            radius: radius
        )
    }
}

struct CategoryData: Codable {
    let id: UUID
    let name: String
    let color: String
    let icon: String
    let createdAt: Date

    enum CodingKeys: String, CodingKey {
        case id, name, color, icon
        case createdAt = "created_at"
    }

    init(from category: TaskCategory) {
        self.id = category.id
        self.name = category.name
        self.color = category.color
        self.icon = category.icon
        self.createdAt = category.createdAt
    }

    func toTaskCategory() -> TaskCategory {
        return TaskCategory(name: name, color: color, icon: icon)
    }
}

struct RecurrenceData: Codable {
    let type: String
    let interval: Int
    let daysOfWeek: [Int]?
    let endDate: Date?

    enum CodingKeys: String, CodingKey {
        case type, interval
        case daysOfWeek = "days_of_week"
        case endDate = "end_date"
    }

    init(from recurrence: RecurrencePattern) {
        self.type = recurrence.type.rawValue
        self.interval = recurrence.interval
        self.daysOfWeek = recurrence.daysOfWeek
        self.endDate = recurrence.endDate
    }

    func toRecurrencePattern() -> RecurrencePattern {
        return RecurrencePattern(
            type: RecurrencePattern.RecurrenceType(rawValue: type) ?? .daily,
            interval: interval,
            daysOfWeek: daysOfWeek,
            endDate: endDate
        )
    }
}
