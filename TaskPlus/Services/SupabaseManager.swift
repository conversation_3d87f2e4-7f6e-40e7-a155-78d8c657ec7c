//
//  SupabaseManager.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import Supabase

// MARK: - Supabase Manager
@MainActor
class SupabaseManager: ObservableObject {
    static let shared = SupabaseManager()
    
    // MARK: - Supabase Client
    private let supabase: SupabaseClient
    
    // MARK: - Published Properties
    @Published var isConnected = false
    @Published var currentUser: User?
    @Published var isLoading = false
    @Published var errorMessage: String?
    
    private init() {
        // Initialize Supabase client
        self.supabase = SupabaseClient(
            supabaseURL: URL(string: "https://bvqwlkudghfrrugjbvbh.supabase.co")!,
            supabaseKey: "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ2cXdsa3VkZ2hmcnJ1Z2pidmJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODg4MjIsImV4cCI6MjA0OTM2NDgyMn0.placeholder_key"
        )
        
        checkConnection()
    }
    
    // MARK: - Connection Management
    private func checkConnection() {
        _Concurrency.Task {
            do {
                // Test connection with a simple query
                let _: [Profile] = try await supabase
                    .from("profiles")
                    .select("id")
                    .limit(1)
                    .execute()
                    .value
                
                await MainActor.run {
                    isConnected = true
                }
            } catch {
                await MainActor.run {
                    isConnected = false
                    errorMessage = "Failed to connect to Supabase: \(error.localizedDescription)"
                }
            }
        }
    }
    
    // MARK: - Authentication
    func signUp(email: String, password: String, userData: [String: Any]) async throws -> User {
        isLoading = true
        errorMessage = nil
        
        do {
            let authResponse = try await supabase.auth.signUp(
                email: email,
                password: password,
                data: userData
            )
            
            guard let user = authResponse.user else {
                throw SupabaseError.authenticationFailed
            }
            
            // Create local user object
            let localUser = User(
                id: UUID(uuidString: user.id.uuidString) ?? UUID(),
                username: userData["username"] as? String ?? "",
                displayName: userData["display_name"] as? String ?? "",
                email: email
            )
            
            currentUser = localUser
            isLoading = false
            return localUser
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    func signIn(email: String, password: String) async throws -> User {
        isLoading = true
        errorMessage = nil
        
        do {
            let authResponse = try await supabase.auth.signIn(
                email: email,
                password: password
            )
            
            guard let user = authResponse.user else {
                throw SupabaseError.authenticationFailed
            }
            
            // Fetch user profile
            let profile: Profile = try await supabase
                .from("profiles")
                .select()
                .eq("id", value: user.id)
                .single()
                .execute()
                .value
            
            let localUser = User(
                id: UUID(uuidString: profile.id.uuidString) ?? UUID(),
                username: profile.username,
                displayName: profile.displayName,
                email: profile.email,
                bio: profile.bio,
                avatarURL: profile.avatarUrl
            )
            
            currentUser = localUser
            isLoading = false
            return localUser
            
        } catch {
            isLoading = false
            errorMessage = error.localizedDescription
            throw error
        }
    }
    
    func signOut() async throws {
        try await supabase.auth.signOut()
        currentUser = nil
    }
    
    // MARK: - Profile Management
    func updateProfile(_ user: User) async throws {
        guard let currentUserId = supabase.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }
        
        let profileUpdate = ProfileUpdate(
            username: user.username,
            displayName: user.displayName,
            bio: user.bio,
            avatarUrl: user.avatarURL,
            updatedAt: Date()
        )
        
        try await supabase
            .from("profiles")
            .update(profileUpdate)
            .eq("id", value: currentUserId)
            .execute()
        
        currentUser = user
    }
    
    // MARK: - Task Management
    func createTask(_ task: Task) async throws -> Task {
        guard let currentUserId = supabase.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }
        
        let taskInsert = TaskInsert(
            title: task.title,
            description: task.description,
            dueDate: task.dueDate,
            priority: task.priority.rawValue,
            status: task.status.rawValue,
            taskType: task.type.rawValue,
            createdBy: currentUserId,
            groupId: task.groupId
        )
        
        let insertedTask: TaskResponse = try await supabase
            .from("tasks")
            .insert(taskInsert)
            .select()
            .single()
            .execute()
            .value
        
        return insertedTask.toTask()
    }
    
    func fetchTasks() async throws -> [Task] {
        guard let currentUserId = supabase.auth.currentUser?.id else {
            throw SupabaseError.notAuthenticated
        }
        
        let taskResponses: [TaskResponse] = try await supabase
            .from("tasks")
            .select()
            .eq("created_by", value: currentUserId)
            .order("created_at", ascending: false)
            .execute()
            .value
        
        return taskResponses.map { $0.toTask() }
    }
    
    func updateTask(_ task: Task) async throws {
        let taskUpdate = TaskUpdate(
            title: task.title,
            description: task.description,
            dueDate: task.dueDate,
            priority: task.priority.rawValue,
            status: task.status.rawValue,
            completedAt: task.completedAt,
            updatedAt: Date()
        )
        
        try await supabase
            .from("tasks")
            .update(taskUpdate)
            .eq("id", value: task.id)
            .execute()
    }
    
    func deleteTask(_ taskId: UUID) async throws {
        try await supabase
            .from("tasks")
            .delete()
            .eq("id", value: taskId)
            .execute()
    }
    
    // MARK: - Error Handling
    func clearError() {
        errorMessage = nil
    }
}

// MARK: - Supabase Errors
enum SupabaseError: LocalizedError {
    case notAuthenticated
    case authenticationFailed
    case networkError
    case unknownError
    
    var errorDescription: String? {
        switch self {
        case .notAuthenticated:
            return "User not authenticated"
        case .authenticationFailed:
            return "Authentication failed"
        case .networkError:
            return "Network connection error"
        case .unknownError:
            return "An unknown error occurred"
        }
    }
}

// MARK: - Supabase Data Models
struct Profile: Codable {
    let id: UUID
    let username: String
    let displayName: String
    let email: String
    let bio: String?
    let avatarUrl: String?
    let privacySetting: String
    let stats: [String: Any]?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id, username, email, bio, stats
        case displayName = "display_name"
        case avatarUrl = "avatar_url"
        case privacySetting = "privacy_setting"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
}

struct ProfileUpdate: Codable {
    let username: String
    let displayName: String
    let bio: String?
    let avatarUrl: String?
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case username, bio
        case displayName = "display_name"
        case avatarUrl = "avatar_url"
        case updatedAt = "updated_at"
    }
}

struct TaskInsert: Codable {
    let title: String
    let description: String?
    let dueDate: Date?
    let priority: String
    let status: String
    let taskType: String
    let createdBy: UUID
    let groupId: UUID?
    
    enum CodingKeys: String, CodingKey {
        case title, description, priority, status
        case dueDate = "due_date"
        case taskType = "task_type"
        case createdBy = "created_by"
        case groupId = "group_id"
    }
}

struct TaskResponse: Codable {
    let id: UUID
    let title: String
    let description: String?
    let dueDate: Date?
    let priority: String
    let status: String
    let taskType: String
    let createdBy: UUID
    let groupId: UUID?
    let completedAt: Date?
    let createdAt: Date
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case id, title, description, priority, status
        case dueDate = "due_date"
        case taskType = "task_type"
        case createdBy = "created_by"
        case groupId = "group_id"
        case completedAt = "completed_at"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
    }
    
    func toTask() -> Task {
        return Task(
            id: id,
            title: title,
            description: description,
            dueDate: dueDate,
            priority: Task.Priority(rawValue: priority) ?? .medium,
            status: Task.Status(rawValue: status) ?? .inProgress,
            type: Task.TaskType(rawValue: taskType) ?? .personal,
            groupId: groupId,
            completedAt: completedAt,
            createdAt: createdAt,
            updatedAt: updatedAt
        )
    }
}

struct TaskUpdate: Codable {
    let title: String
    let description: String?
    let dueDate: Date?
    let priority: String
    let status: String
    let completedAt: Date?
    let updatedAt: Date
    
    enum CodingKeys: String, CodingKey {
        case title, description, priority, status
        case dueDate = "due_date"
        case completedAt = "completed_at"
        case updatedAt = "updated_at"
    }
}
