//
//  SettingsManager.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import SwiftUI

// MARK: - Settings Manager
@MainActor
class SettingsManager: ObservableObject {
    static let shared = SettingsManager()
    
    // MARK: - App Settings
    @Published var appSettings: AppSettings {
        didSet {
            saveSettings()
        }
    }
    
    // MARK: - Theme Settings
    @Published var currentTheme: AppTheme {
        didSet {
            saveSettings()
        }
    }
    
    private let userDefaults = UserDefaults.standard
    private let settingsKey = "TaskMateAppSettings"
    private let themeKey = "TaskMateAppTheme"
    
    private init() {
        self.appSettings = Self.loadSettings()
        self.currentTheme = Self.loadTheme()
    }
    
    // MARK: - Settings Persistence
    private func saveSettings() {
        if let encoded = try? JSONEncoder().encode(appSettings) {
            userDefaults.set(encoded, forKey: settingsKey)
        }
        
        if let themeEncoded = try? JSONEncoder().encode(currentTheme) {
            userDefaults.set(themeEncoded, forKey: themeKey)
        }
    }
    
    private static func loadSettings() -> AppSettings {
        guard let data = UserDefaults.standard.data(forKey: "TaskMateAppSettings"),
              let settings = try? JSONDecoder().decode(AppSettings.self, from: data) else {
            return AppSettings()
        }
        return settings
    }
    
    private static func loadTheme() -> AppTheme {
        guard let data = UserDefaults.standard.data(forKey: "TaskMateAppTheme"),
              let theme = try? JSONDecoder().decode(AppTheme.self, from: data) else {
            return .sunrise
        }
        return theme
    }
    
    // MARK: - Settings Updates
    func updateLanguage(_ language: AppLanguage) {
        appSettings.language = language
    }
    
    func updateDateFormat(_ format: DateFormat) {
        appSettings.dateFormat = format
    }
    
    func updateTimeFormat(_ format: TimeFormat) {
        appSettings.timeFormat = format
    }
    
    func updateStartOfWeek(_ day: WeekDay) {
        appSettings.startOfWeek = day
    }
    
    func updateTheme(_ theme: AppTheme) {
        currentTheme = theme
    }
    
    func toggleHapticFeedback() {
        appSettings.hapticFeedbackEnabled.toggle()
    }
    
    func toggleSoundEffects() {
        appSettings.soundEffectsEnabled.toggle()
    }
    
    func toggleAutoSync() {
        appSettings.autoSyncEnabled.toggle()
    }
    
    func updateSyncInterval(_ interval: SyncInterval) {
        appSettings.syncInterval = interval
    }
    
    // MARK: - Privacy Settings
    func updateProfileVisibility(_ visibility: ProfileVisibility) {
        appSettings.profileVisibility = visibility
    }
    
    func toggleDataCollection() {
        appSettings.allowDataCollection.toggle()
    }
    
    func toggleAnalytics() {
        appSettings.allowAnalytics.toggle()
    }
    
    // MARK: - Reset Settings
    func resetToDefaults() {
        appSettings = AppSettings()
        currentTheme = .sunrise
    }
    
    func resetNotificationSettings() {
        appSettings.notificationSettings = NotificationSettings()
    }
    
    // MARK: - Export/Import Settings
    func exportSettings() -> Data? {
        let exportData = SettingsExport(
            appSettings: appSettings,
            theme: currentTheme,
            exportDate: Date(),
            appVersion: Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        )
        
        return try? JSONEncoder().encode(exportData)
    }
    
    func importSettings(from data: Data) -> Bool {
        guard let importData = try? JSONDecoder().decode(SettingsExport.self, from: data) else {
            return false
        }
        
        appSettings = importData.appSettings
        currentTheme = importData.theme
        return true
    }
}

// MARK: - App Settings Model
struct AppSettings: Codable {
    // MARK: - General Settings
    var language: AppLanguage = .english
    var dateFormat: DateFormat = .monthDayYear
    var timeFormat: TimeFormat = .twelveHour
    var startOfWeek: WeekDay = .monday
    
    // MARK: - Notification Settings
    var notificationSettings = NotificationSettings()
    
    // MARK: - Privacy Settings
    var profileVisibility: ProfileVisibility = .friends
    var allowDataCollection = true
    var allowAnalytics = true
    
    // MARK: - App Behavior
    var hapticFeedbackEnabled = true
    var soundEffectsEnabled = true
    var autoSyncEnabled = true
    var syncInterval: SyncInterval = .fifteenMinutes
    
    // MARK: - Task Settings
    var defaultTaskPriority: Task.Priority = .medium
    var defaultReminderTime = 30 // minutes before due date
    var showCompletedTasks = true
    var autoArchiveCompletedTasks = false
    var archiveAfterDays = 30
    
    // MARK: - Group Settings
    var autoJoinPublicGroups = false
    var allowGroupInvitations = true
    var showGroupNotifications = true
}

// MARK: - Enums
enum ProfileVisibility: String, CaseIterable, Codable {
    case everyone = "everyone"
    case friends = "friends"
    case privateProfile = "private"

    var displayName: String {
        switch self {
        case .everyone: return "Everyone"
        case .friends: return "Friends Only"
        case .privateProfile: return "Private"
        }
    }
}

enum AppLanguage: String, CaseIterable, Codable {
    case english = "en"
    case arabic = "ar"
    case spanish = "es"
    case french = "fr"
    case german = "de"
    
    var displayName: String {
        switch self {
        case .english: return "English"
        case .arabic: return "العربية"
        case .spanish: return "Español"
        case .french: return "Français"
        case .german: return "Deutsch"
        }
    }
}

enum DateFormat: String, CaseIterable, Codable {
    case monthDayYear = "MM/dd/yyyy"
    case dayMonthYear = "dd/MM/yyyy"
    case yearMonthDay = "yyyy/MM/dd"
    case monthDayYearLong = "MMMM d, yyyy"
    
    var displayName: String {
        let formatter = DateFormatter()
        formatter.dateFormat = rawValue
        return formatter.string(from: Date())
    }
}

enum TimeFormat: String, CaseIterable, Codable {
    case twelveHour = "h:mm a"
    case twentyFourHour = "HH:mm"
    
    var displayName: String {
        switch self {
        case .twelveHour: return "12-hour (1:30 PM)"
        case .twentyFourHour: return "24-hour (13:30)"
        }
    }
}

enum WeekDay: Int, CaseIterable, Codable {
    case sunday = 1
    case monday = 2
    case tuesday = 3
    case wednesday = 4
    case thursday = 5
    case friday = 6
    case saturday = 7
    
    var displayName: String {
        switch self {
        case .sunday: return "Sunday"
        case .monday: return "Monday"
        case .tuesday: return "Tuesday"
        case .wednesday: return "Wednesday"
        case .thursday: return "Thursday"
        case .friday: return "Friday"
        case .saturday: return "Saturday"
        }
    }
}

enum SyncInterval: Int, CaseIterable, Codable {
    case fiveMinutes = 5
    case fifteenMinutes = 15
    case thirtyMinutes = 30
    case oneHour = 60
    case manual = 0
    
    var displayName: String {
        switch self {
        case .fiveMinutes: return "Every 5 minutes"
        case .fifteenMinutes: return "Every 15 minutes"
        case .thirtyMinutes: return "Every 30 minutes"
        case .oneHour: return "Every hour"
        case .manual: return "Manual only"
        }
    }
}

enum AppTheme: String, CaseIterable, Codable {
    case sunrise = "sunrise"
    case sunset = "sunset"
    case ocean = "ocean"
    case forest = "forest"
    case midnight = "midnight"
    
    var displayName: String {
        switch self {
        case .sunrise: return "Sunrise"
        case .sunset: return "Sunset"
        case .ocean: return "Ocean"
        case .forest: return "Forest"
        case .midnight: return "Midnight"
        }
    }
    
    var primaryColor: Color {
        switch self {
        case .sunrise: return DesignSystem.Colors.sunriseOrange
        case .sunset: return DesignSystem.Colors.sunsetCoral
        case .ocean: return .blue
        case .forest: return .green
        case .midnight: return .purple
        }
    }
}

// MARK: - Settings Export Model
struct SettingsExport: Codable {
    let appSettings: AppSettings
    let theme: AppTheme
    let exportDate: Date
    let appVersion: String
}
