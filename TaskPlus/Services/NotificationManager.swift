//
//  NotificationManager.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import UserNotifications
import SwiftUI

// MARK: - Notification Manager
@MainActor
class NotificationManager: ObservableObject {
    static let shared = NotificationManager()
    
    @Published var isAuthorized = false
    @Published var notificationSettings: NotificationSettings = NotificationSettings()
    
    private init() {
        checkAuthorizationStatus()
    }
    
    // MARK: - Authorization
    func requestAuthorization() async {
        do {
            let granted = try await UNUserNotificationCenter.current().requestAuthorization(
                options: [.alert, .badge, .sound]
            )
            isAuthorized = granted
        } catch {
            print("Failed to request notification authorization: \(error)")
        }
    }
    
    private func checkAuthorizationStatus() {
        UNUserNotificationCenter.current().getNotificationSettings { settings in
            DispatchQueue.main.async {
                self.isAuthorized = settings.authorizationStatus == .authorized
            }
        }
    }
    
    // MARK: - Task Notifications
    func scheduleTaskReminder(for task: Task, minutesBefore: Int = 30) {
        guard isAuthorized, let dueDate = task.dueDate else { return }
        
        let reminderDate = Calendar.current.date(
            byAdding: .minute,
            value: -minutesBefore,
            to: dueDate
        ) ?? dueDate
        
        // Don't schedule notifications for past dates
        guard reminderDate > Date() else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Task Reminder"
        content.body = "Don't forget: \(task.title)"
        content.sound = .default
        content.badge = 1
        
        // Add task priority to notification
        switch task.priority {
        case .high:
            content.subtitle = "High Priority"
        case .medium:
            content.subtitle = "Medium Priority"
        case .low:
            content.subtitle = "Low Priority"
        }
        
        let calendar = Calendar.current
        let dateComponents = calendar.dateComponents(
            [.year, .month, .day, .hour, .minute],
            from: reminderDate
        )
        
        let trigger = UNCalendarNotificationTrigger(
            dateMatching: dateComponents,
            repeats: false
        )
        
        let request = UNNotificationRequest(
            identifier: "task-\(task.id.uuidString)",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request) { error in
            if let error = error {
                print("Failed to schedule notification: \(error)")
            }
        }
    }
    
    func cancelTaskReminder(for task: Task) {
        let identifier = "task-\(task.id.uuidString)"
        UNUserNotificationCenter.current().removePendingNotificationRequests(
            withIdentifiers: [identifier]
        )
    }
    
    // MARK: - Group Notifications
    func scheduleGroupActivityNotification(groupName: String, activity: String) {
        guard isAuthorized else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Group Activity"
        content.body = "\(activity) in \(groupName)"
        content.sound = .default
        content.badge = 1
        
        // Schedule for immediate delivery
        let trigger = UNTimeIntervalNotificationTrigger(timeInterval: 1, repeats: false)
        
        let request = UNNotificationRequest(
            identifier: "group-\(UUID().uuidString)",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request)
    }
    
    // MARK: - Daily Summary
    func scheduleDailySummary() {
        guard isAuthorized, notificationSettings.dailySummaryEnabled else { return }
        
        let content = UNMutableNotificationContent()
        content.title = "Daily Summary"
        content.body = "Check your tasks and progress for today"
        content.sound = .default
        
        // Schedule for the time set in settings
        var dateComponents = DateComponents()
        dateComponents.hour = notificationSettings.dailySummaryTime.hour
        dateComponents.minute = notificationSettings.dailySummaryTime.minute
        
        let trigger = UNCalendarNotificationTrigger(
            dateMatching: dateComponents,
            repeats: true
        )
        
        let request = UNNotificationRequest(
            identifier: "daily-summary",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request)
    }
    
    func cancelDailySummary() {
        UNUserNotificationCenter.current().removePendingNotificationRequests(
            withIdentifiers: ["daily-summary"]
        )
    }
    
    // MARK: - Motivational Notifications
    func scheduleMotivationalMessage() {
        guard isAuthorized, notificationSettings.motivationalMessagesEnabled else { return }
        
        let messages = [
            "You're doing great! Keep up the momentum! 🌟",
            "Every small step counts towards your goals! 🚀",
            "Believe in yourself - you've got this! 💪",
            "Progress, not perfection. You're on the right track! ✨",
            "Your future self will thank you for today's efforts! 🌅"
        ]
        
        let content = UNMutableNotificationContent()
        content.title = "Stay Motivated!"
        content.body = messages.randomElement() ?? "Keep pushing forward!"
        content.sound = .default
        
        // Schedule randomly between 1-4 hours from now
        let randomInterval = Double.random(in: 3600...14400) // 1-4 hours
        let trigger = UNTimeIntervalNotificationTrigger(
            timeInterval: randomInterval,
            repeats: false
        )
        
        let request = UNNotificationRequest(
            identifier: "motivation-\(UUID().uuidString)",
            content: content,
            trigger: trigger
        )
        
        UNUserNotificationCenter.current().add(request)
    }
    
    // MARK: - Settings Management
    func updateNotificationSettings(_ settings: NotificationSettings) {
        notificationSettings = settings
        
        // Update scheduled notifications based on new settings
        if settings.dailySummaryEnabled {
            scheduleDailySummary()
        } else {
            cancelDailySummary()
        }
    }
    
    // MARK: - Badge Management
    func updateBadgeCount(_ count: Int) {
        UNUserNotificationCenter.current().setBadgeCount(count)
    }
    
    func clearBadge() {
        updateBadgeCount(0)
    }
}

// MARK: - Notification Settings
struct NotificationSettings: Codable {
    var taskRemindersEnabled = true
    var groupNotificationsEnabled = true
    var dailySummaryEnabled = true
    var motivationalMessagesEnabled = true
    var dailySummaryTime = NotificationTime(hour: 9, minute: 0) // 9:00 AM
    var reminderMinutesBefore = 30
    
    struct NotificationTime: Codable {
        var hour: Int
        var minute: Int
        
        var displayString: String {
            let formatter = DateFormatter()
            formatter.timeStyle = .short
            
            var components = DateComponents()
            components.hour = hour
            components.minute = minute
            
            if let date = Calendar.current.date(from: components) {
                return formatter.string(from: date)
            }
            return "\(hour):\(String(format: "%02d", minute))"
        }
    }
}

// MARK: - Notification Extensions
extension NotificationManager {
    func scheduleAllTaskReminders() {
        let tasks = DataManager.shared.tasks.filter { task in
            task.status != .completed && task.dueDate != nil
        }
        
        for task in tasks {
            scheduleTaskReminder(for: task, minutesBefore: notificationSettings.reminderMinutesBefore)
        }
    }
    
    func cancelAllTaskReminders() {
        let tasks = DataManager.shared.tasks
        for task in tasks {
            cancelTaskReminder(for: task)
        }
    }
}
