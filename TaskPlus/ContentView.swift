//
//  ContentView.swift
//  TaskPlus
//
//  Created by m<PERSON><PERSON><PERSON> on 14/12/1446 AH.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var authManager = AuthenticationManager.shared
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var settingsManager: SettingsManager

    @State private var showProfileSetup = false

    var body: some View {
        SwiftUI.Group {
            if authManager.isAuthenticated {
                // User is logged in - show main app
                MainTabView()
                    .environmentObject(authManager)
                    .onAppear {
                        checkProfileCompletion()
                    }
            } else {
                // User is not logged in - show login screen
                LoginView()
                    .environmentObject(authManager)
            }
        }
        .preferredColorScheme(colorScheme)
        .sheet(isPresented: $showProfileSetup) {
            ProfileSetupView()
                .environmentObject(authManager)
        }
    }

    private var colorScheme: ColorScheme? {
        switch settingsManager.currentTheme {
        case .midnight:
            return .dark
        default:
            return nil // Use system setting
        }
    }

    private func checkProfileCompletion() {
        // Check if user needs to complete profile setup
        if let user = authManager.currentUser,
           user.username.isEmpty || user.username.contains(user.displayName.lowercased()) {
            // Show profile setup if username is empty or auto-generated
            showProfileSetup = true
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(DataManager.shared)
        .environmentObject(SettingsManager.shared)
        .environmentObject(NotificationManager.shared)
}
