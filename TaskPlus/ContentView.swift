//
//  ContentView.swift
//  TaskPlus
//
//  Created by m<PERSON><PERSON><PERSON> on 14/12/1446 AH.
//

import SwiftUI

struct ContentView: View {
    @EnvironmentObject var dataManager: DataManager
    @EnvironmentObject var settingsManager: SettingsManager

    var body: some View {
        MainTabView()
            .preferredColorScheme(colorScheme)
    }

    private var colorScheme: ColorScheme? {
        switch settingsManager.currentTheme {
        case .midnight:
            return .dark
        default:
            return nil // Use system setting
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(DataManager.shared)
        .environmentObject(SettingsManager.shared)
        .environmentObject(NotificationManager.shared)
}
