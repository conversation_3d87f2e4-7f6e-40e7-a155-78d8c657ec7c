//
//  BuildTest.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation
import SwiftUI

// MARK: - Build Test Helper
struct BuildTest {
    
    // Test that all managers can be initialized
    static func testManagersInitialization() -> Bool {
        do {
            let _ = DataManager.shared
            let _ = AuthenticationManager.shared
            let _ = SupabaseManager.shared
            let _ = NotificationManager.shared
            let _ = SettingsManager.shared
            
            print("✅ All managers initialized successfully")
            return true
        } catch {
            print("❌ Manager initialization failed: \(error)")
            return false
        }
    }
    
    // Test that all models can be created
    static func testModelsCreation() -> Bool {
        do {
            let testUser = User(
                username: "testuser",
                displayName: "Test User",
                email: "<EMAIL>"
            )
            
            let testTask = Task(
                title: "Test Task",
                description: "Test Description",
                priority: .medium,
                createdByUserId: testUser.id
            )
            
            let testGroup = Group(
                name: "Test Group",
                description: "Test Group Description",
                ownerId: testUser.id
            )
            
            print("✅ All models created successfully")
            print("   User ID: \(testUser.id)")
            print("   Task ID: \(testTask.id)")
            print("   Group ID: \(testGroup.id)")
            
            return true
        } catch {
            print("❌ Model creation failed: \(error)")
            return false
        }
    }
    
    // Test that design system is accessible
    static func testDesignSystem() -> Bool {
        do {
            let _ = DesignSystem.Colors.primary
            let _ = DesignSystem.Typography.body
            let _ = DesignSystem.Spacing.md
            let _ = DesignSystem.CornerRadius.medium
            
            print("✅ Design system accessible")
            return true
        } catch {
            print("❌ Design system test failed: \(error)")
            return false
        }
    }
    
    // Test Supabase configuration
    static func testSupabaseConfig() -> Bool {
        let isValid = SupabaseConfig.validateConfiguration()
        if isValid {
            print("✅ Supabase configuration is valid")
        } else {
            print("❌ Supabase configuration is invalid")
        }
        return isValid
    }
    
    // Run all tests
    static func runAllTests() -> Bool {
        print("🧪 Running build tests...")
        
        let managersTest = testManagersInitialization()
        let modelsTest = testModelsCreation()
        let designTest = testDesignSystem()
        let supabaseTest = testSupabaseConfig()
        
        let allPassed = managersTest && modelsTest && designTest && supabaseTest
        
        if allPassed {
            print("🎉 All build tests passed!")
        } else {
            print("❌ Some build tests failed")
        }
        
        return allPassed
    }
}

// MARK: - Build Test View (for debugging)
struct BuildTestView: View {
    @State private var testResults: [String] = []
    @State private var allTestsPassed = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Build Test Results")
                    .font(.title)
                    .fontWeight(.bold)
                
                if testResults.isEmpty {
                    Button("Run Tests") {
                        runTests()
                    }
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                } else {
                    ScrollView {
                        VStack(alignment: .leading, spacing: 8) {
                            ForEach(testResults, id: \.self) { result in
                                Text(result)
                                    .font(.system(.body, design: .monospaced))
                                    .foregroundColor(result.contains("✅") ? .green : .red)
                            }
                        }
                        .padding()
                    }
                    
                    Button("Run Tests Again") {
                        runTests()
                    }
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(8)
                }
                
                if allTestsPassed {
                    Text("🎉 Ready for production!")
                        .font(.headline)
                        .foregroundColor(.green)
                        .padding()
                        .background(Color.green.opacity(0.1))
                        .cornerRadius(8)
                }
                
                Spacer()
            }
            .padding()
            .navigationTitle("Build Tests")
        }
    }
    
    private func runTests() {
        testResults.clear()
        
        // Capture print output
        let managersTest = BuildTest.testManagersInitialization()
        let modelsTest = BuildTest.testModelsCreation()
        let designTest = BuildTest.testDesignSystem()
        let supabaseTest = BuildTest.testSupabaseConfig()
        
        testResults = [
            managersTest ? "✅ Managers: PASSED" : "❌ Managers: FAILED",
            modelsTest ? "✅ Models: PASSED" : "❌ Models: FAILED",
            designTest ? "✅ Design System: PASSED" : "❌ Design System: FAILED",
            supabaseTest ? "✅ Supabase Config: PASSED" : "❌ Supabase Config: FAILED"
        ]
        
        allTestsPassed = managersTest && modelsTest && designTest && supabaseTest
    }
}

#Preview {
    BuildTestView()
}
