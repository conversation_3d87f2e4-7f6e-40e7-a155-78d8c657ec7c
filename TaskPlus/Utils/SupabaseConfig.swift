//
//  SupabaseConfig.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import Foundation

// MARK: - Supabase Configuration
struct SupabaseConfig {
    
    // MARK: - Project Configuration
    static let projectURL = "https://bvqwlkudghfrrugjbvbh.supabase.co"
    static let projectRef = "bvqwlkudghfrrugjbvbh"
    
    // MARK: - API Keys
    // Note: In production, these should be stored securely
    static let anonKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImJ2cXdsa3VkZ2hmcnJ1Z2pidmJoIiwicm9sZSI6ImFub24iLCJpYXQiOjE3MzM3ODg4MjIsImV4cCI6MjA0OTM2NDgyMn0.placeholder_key"
    
    // MARK: - Database Tables
    enum Tables {
        static let profiles = "profiles"
        static let tasks = "tasks"
        static let groups = "groups"
        static let groupMembers = "group_members"
        static let friendships = "friendships"
        static let messages = "messages"
    }
    
    // MARK: - Real-time Channels
    enum Channels {
        static let tasks = "tasks_channel"
        static let groups = "groups_channel"
        static let messages = "messages_channel"
        static let friendships = "friendships_channel"
    }
    
    // MARK: - Storage Buckets
    enum Storage {
        static let avatars = "avatars"
        static let taskAttachments = "task_attachments"
        static let groupImages = "group_images"
    }
    
    // MARK: - Configuration Validation
    static func validateConfiguration() -> Bool {
        guard !projectURL.isEmpty,
              !projectRef.isEmpty,
              !anonKey.isEmpty else {
            print("❌ Supabase configuration is incomplete")
            return false
        }
        
        guard URL(string: projectURL) != nil else {
            print("❌ Invalid Supabase project URL")
            return false
        }
        
        print("✅ Supabase configuration is valid")
        return true
    }
    
    // MARK: - Environment Detection
    static var isProduction: Bool {
        #if DEBUG
        return false
        #else
        return true
        #endif
    }
    
    // MARK: - Logging Configuration
    static var enableLogging: Bool {
        return !isProduction
    }
}

// MARK: - Supabase Error Handling
extension SupabaseConfig {
    
    enum ConfigurationError: LocalizedError {
        case invalidURL
        case missingAPIKey
        case invalidConfiguration
        
        var errorDescription: String? {
            switch self {
            case .invalidURL:
                return "Invalid Supabase project URL"
            case .missingAPIKey:
                return "Missing Supabase API key"
            case .invalidConfiguration:
                return "Invalid Supabase configuration"
            }
        }
    }
}

// MARK: - Development Helpers
#if DEBUG
extension SupabaseConfig {
    
    // MARK: - Debug Information
    static func printConfiguration() {
        print("🔧 Supabase Configuration:")
        print("   Project URL: \(projectURL)")
        print("   Project Ref: \(projectRef)")
        print("   Environment: \(isProduction ? "Production" : "Development")")
        print("   Logging: \(enableLogging ? "Enabled" : "Disabled")")
    }
    
    // MARK: - Test Connection
    static func testConnection() async -> Bool {
        guard validateConfiguration() else {
            return false
        }
        
        // This would typically test the connection to Supabase
        // For now, we'll just validate the configuration
        return true
    }
}
#endif
