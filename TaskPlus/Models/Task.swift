//
//  Task.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation

// MARK: - Task Model
struct Task: Identifiable, Codable {
    let id: UUID
    var title: String
    var description: String?
    var dueDate: Date?
    var priority: Priority
    var status: TaskStatus
    var tags: [String]
    var estimatedDuration: TimeInterval? // in seconds
    var actualDuration: TimeInterval? // in seconds
    var createdAt: Date
    var updatedAt: Date
    var completedAt: Date?
    
    // Task Type
    var taskType: TaskType
    
    // For group tasks
    var groupId: UUID?
    var assignedToUserId: UUID?
    var createdByUserId: UUID
    
    // Recurrence for personal tasks
    var recurrence: RecurrencePattern?

    // MARK: - Enhanced Features (Phase 3.1)
    var subtasks: [Subtask]
    var attachments: [TaskAttachment]
    var location: TaskLocation?
    var reminderTime: Date?
    var isImportant: Bool
    var difficulty: Difficulty
    var category: TaskCategory?
    
    init(title: String, description: String? = nil, dueDate: Date? = nil, priority: Priority = .medium, createdByUserId: UUID, taskType: TaskType = .personal) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.dueDate = dueDate
        self.priority = priority
        self.status = .active
        self.tags = []
        self.estimatedDuration = nil
        self.actualDuration = nil
        self.createdAt = Date()
        self.updatedAt = Date()
        self.completedAt = nil
        self.taskType = taskType
        self.groupId = nil
        self.assignedToUserId = nil
        self.createdByUserId = createdByUserId
        self.recurrence = nil

        // Enhanced features defaults
        self.subtasks = []
        self.attachments = []
        self.location = nil
        self.reminderTime = nil
        self.isImportant = false
        self.difficulty = .medium
        self.category = nil
    }

    // Initializer for Supabase data with existing ID
    init(id: UUID, title: String, description: String? = nil, dueDate: Date? = nil, priority: Priority = .medium, status: TaskStatus = .active, createdByUserId: UUID, taskType: TaskType = .personal, groupId: UUID? = nil, completedAt: Date? = nil, createdAt: Date = Date(), updatedAt: Date = Date()) {
        self.id = id
        self.title = title
        self.description = description
        self.dueDate = dueDate
        self.priority = priority
        self.status = status
        self.tags = []
        self.estimatedDuration = nil
        self.actualDuration = nil
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.completedAt = completedAt
        self.taskType = taskType
        self.groupId = groupId
        self.assignedToUserId = nil
        self.createdByUserId = createdByUserId
        self.recurrence = nil

        // Enhanced features defaults
        self.subtasks = []
        self.attachments = []
        self.location = nil
        self.reminderTime = nil
        self.isImportant = false
        self.difficulty = .medium
        self.category = nil
    }
}

// MARK: - Task Enums
extension Task {
    enum Priority: String, Codable, CaseIterable {
        case low = "low"
        case medium = "medium"
        case high = "high"
        
        var displayName: String {
            switch self {
            case .low: return "Low"
            case .medium: return "Medium"
            case .high: return "High"
            }
        }
        
        var sortOrder: Int {
            switch self {
            case .high: return 3
            case .medium: return 2
            case .low: return 1
            }
        }
    }
    
    enum TaskStatus: String, Codable, CaseIterable {
        case draft = "draft"
        case active = "active"
        case inProgress = "in_progress"
        case completed = "completed"
        case cancelled = "cancelled"
        case archived = "archived"

        var displayName: String {
            switch self {
            case .draft: return "Draft"
            case .active: return "Active"
            case .inProgress: return "In Progress"
            case .completed: return "Completed"
            case .cancelled: return "Cancelled"
            case .archived: return "Archived"
            }
        }
    }
    
    enum TaskType: String, Codable {
        case personal = "personal"
        case group = "group"
    }
}

// MARK: - Recurrence Pattern
struct RecurrencePattern: Codable {
    var type: RecurrenceType
    var interval: Int // e.g., every 2 days, every 3 weeks
    var daysOfWeek: [Int]? // 1-7 for Sunday-Saturday
    var endDate: Date?
    
    enum RecurrenceType: String, Codable, CaseIterable {
        case daily = "daily"
        case weekly = "weekly"
        case monthly = "monthly"
        case custom = "custom"
        
        var displayName: String {
            switch self {
            case .daily: return "Daily"
            case .weekly: return "Weekly"
            case .monthly: return "Monthly"
            case .custom: return "Custom"
            }
        }
    }
}

// MARK: - Task Extensions
extension Task {
    // Check if task is overdue
    var isOverdue: Bool {
        guard let dueDate = dueDate, status != .completed else { return false }
        return dueDate < Date()
    }
    
    // Check if task is due today
    var isDueToday: Bool {
        guard let dueDate = dueDate else { return false }
        return Calendar.current.isDateInToday(dueDate)
    }
    
    // Check if task is due this week
    var isDueThisWeek: Bool {
        guard let dueDate = dueDate else { return false }
        let calendar = Calendar.current
        let now = Date()
        let weekFromNow = calendar.date(byAdding: .weekOfYear, value: 1, to: now) ?? now
        return dueDate >= now && dueDate <= weekFromNow
    }
    
    // Get time until due
    var timeUntilDue: String? {
        guard let dueDate = dueDate else { return nil }
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: dueDate, relativeTo: Date())
    }
    
    // Mark task as completed
    mutating func markCompleted() {
        status = .completed
        completedAt = Date()
        updatedAt = Date()
    }
    
    // Mark task as active
    mutating func markActive() {
        status = .active
        completedAt = nil
        updatedAt = Date()
    }
    
    // Update task
    mutating func update(title: String? = nil, description: String? = nil, dueDate: Date? = nil, priority: Priority? = nil, tags: [String]? = nil) {
        if let title = title { self.title = title }
        if let description = description { self.description = description }
        if let dueDate = dueDate { self.dueDate = dueDate }
        if let priority = priority { self.priority = priority }
        if let tags = tags { self.tags = tags }
        self.updatedAt = Date()
    }
    
    // Check if task matches search criteria
    func matchesSearch(_ searchText: String) -> Bool {
        let lowercasedSearch = searchText.lowercased()
        return title.lowercased().contains(lowercasedSearch) ||
               description?.lowercased().contains(lowercasedSearch) == true ||
               tags.contains { $0.lowercased().contains(lowercasedSearch) }
    }
    
    // Check if task matches filter criteria
    func matchesFilter(priority: Priority? = nil, status: TaskStatus? = nil, tags: [String]? = nil, dueDateRange: ClosedRange<Date>? = nil) -> Bool {
        if let filterPriority = priority, self.priority != filterPriority {
            return false
        }
        
        if let filterStatus = status, self.status != filterStatus {
            return false
        }
        
        if let filterTags = tags, !filterTags.isEmpty {
            let hasMatchingTag = filterTags.contains { filterTag in
                self.tags.contains { taskTag in
                    taskTag.lowercased().contains(filterTag.lowercased())
                }
            }
            if !hasMatchingTag { return false }
        }
        
        if let dateRange = dueDateRange, let taskDueDate = dueDate {
            if !dateRange.contains(taskDueDate) {
                return false
            }
        }
        
        return true
    }
}

// MARK: - Sample Data
extension Task {
    static func samplePersonalTasks(for userId: UUID) -> [Task] {
        [
            Task(title: "Review quarterly marketing report", description: "Analyze Q4 performance metrics and prepare insights for team meeting", dueDate: Calendar.current.date(byAdding: .day, value: 1, to: Date()), priority: .high, createdByUserId: userId),
            Task(title: "Morning workout routine", description: "30-minute cardio and strength training", dueDate: Calendar.current.date(byAdding: .hour, value: 2, to: Date()), priority: .medium, createdByUserId: userId),
            Task(title: "Call mom", description: "Weekly check-in call with family", dueDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()), priority: .low, createdByUserId: userId),
            Task(title: "Prepare presentation slides", description: "Create slides for next week's client presentation", dueDate: Calendar.current.date(byAdding: .day, value: 5, to: Date()), priority: .high, createdByUserId: userId)
        ]
    }
    
    static func sampleGroupTasks(for userId: UUID, groupId: UUID) -> [Task] {
        var tasks = [
            Task(title: "Design user interface mockups", description: "Create wireframes and mockups for the new feature", dueDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()), priority: .high, createdByUserId: userId, taskType: .group),
            Task(title: "Research competitor analysis", description: "Analyze top 5 competitors and their features", dueDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()), priority: .medium, createdByUserId: userId, taskType: .group),
            Task(title: "Write project documentation", description: "Document the project requirements and specifications", dueDate: Calendar.current.date(byAdding: .day, value: 10, to: Date()), priority: .medium, createdByUserId: userId, taskType: .group)
        ]
        
        for i in 0..<tasks.count {
            tasks[i].groupId = groupId
        }
        
        return tasks
    }
}

// MARK: - Enhanced Task Features (Phase 3.1)

// MARK: - Subtask
struct Subtask: Identifiable, Codable {
    let id: UUID
    var title: String
    var isCompleted: Bool
    var createdAt: Date

    init(title: String) {
        self.id = UUID()
        self.title = title
        self.isCompleted = false
        self.createdAt = Date()
    }
}

// MARK: - Task Attachment
struct TaskAttachment: Identifiable, Codable {
    let id: UUID
    var name: String
    var type: AttachmentType
    var url: String?
    var localPath: String?
    var size: Int64? // in bytes
    var createdAt: Date

    enum AttachmentType: String, Codable, CaseIterable {
        case image = "image"
        case document = "document"
        case audio = "audio"
        case video = "video"
        case link = "link"

        var displayName: String {
            switch self {
            case .image: return "Image"
            case .document: return "Document"
            case .audio: return "Audio"
            case .video: return "Video"
            case .link: return "Link"
            }
        }

        var systemIcon: String {
            switch self {
            case .image: return "photo"
            case .document: return "doc"
            case .audio: return "waveform"
            case .video: return "video"
            case .link: return "link"
            }
        }
    }

    init(name: String, type: AttachmentType, url: String? = nil) {
        self.id = UUID()
        self.name = name
        self.type = type
        self.url = url
        self.localPath = nil
        self.size = nil
        self.createdAt = Date()
    }
}

// MARK: - Task Location
struct TaskLocation: Codable {
    var name: String
    var address: String?
    var latitude: Double?
    var longitude: Double?
    var radius: Double? // in meters for location-based reminders

    init(name: String, address: String? = nil, latitude: Double? = nil, longitude: Double? = nil, radius: Double? = nil) {
        self.name = name
        self.address = address
        self.latitude = latitude
        self.longitude = longitude
        self.radius = radius
    }
}

// MARK: - Task Difficulty
extension Task {
    enum Difficulty: String, Codable, CaseIterable {
        case easy = "easy"
        case medium = "medium"
        case hard = "hard"
        case expert = "expert"

        var displayName: String {
            switch self {
            case .easy: return "Easy"
            case .medium: return "Medium"
            case .hard: return "Hard"
            case .expert: return "Expert"
            }
        }

        var color: String {
            switch self {
            case .easy: return "green"
            case .medium: return "yellow"
            case .hard: return "orange"
            case .expert: return "red"
            }
        }

        var points: Int {
            switch self {
            case .easy: return 10
            case .medium: return 25
            case .hard: return 50
            case .expert: return 100
            }
        }
    }
}

// MARK: - Task Category
struct TaskCategory: Identifiable, Codable {
    let id: UUID
    var name: String
    var color: String
    var icon: String
    var createdAt: Date

    init(name: String, color: String = "blue", icon: String = "folder") {
        self.id = UUID()
        self.name = name
        self.color = color
        self.icon = icon
        self.createdAt = Date()
    }

    // Predefined categories
    static let work = TaskCategory(name: "Work", color: "blue", icon: "briefcase")
    static let personal = TaskCategory(name: "Personal", color: "green", icon: "person")
    static let health = TaskCategory(name: "Health", color: "red", icon: "heart")
    static let education = TaskCategory(name: "Education", color: "purple", icon: "book")
    static let finance = TaskCategory(name: "Finance", color: "yellow", icon: "dollarsign.circle")
    static let shopping = TaskCategory(name: "Shopping", color: "orange", icon: "cart")
    static let travel = TaskCategory(name: "Travel", color: "teal", icon: "airplane")
    static let home = TaskCategory(name: "Home", color: "brown", icon: "house")

    static let defaultCategories: [TaskCategory] = [
        .work, .personal, .health, .education, .finance, .shopping, .travel, .home
    ]
}
