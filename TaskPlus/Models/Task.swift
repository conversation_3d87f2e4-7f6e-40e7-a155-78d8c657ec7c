//
//  Task.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import Foundation

// MARK: - Task Model
struct Task: Identifiable, Codable {
    let id: UUID
    var title: String
    var description: String?
    var dueDate: Date?
    var priority: Priority
    var status: TaskStatus
    var tags: [String]
    var estimatedDuration: TimeInterval? // in seconds
    var actualDuration: TimeInterval? // in seconds
    var createdAt: Date
    var updatedAt: Date
    var completedAt: Date?
    
    // Task Type
    var taskType: TaskType
    
    // For group tasks
    var groupId: UUID?
    var assignedToUserId: UUID?
    var createdByUserId: UUID
    
    // Recurrence for personal tasks
    var recurrence: RecurrencePattern?
    
    init(title: String, description: String? = nil, dueDate: Date? = nil, priority: Priority = .medium, createdByUserId: UUID, taskType: TaskType = .personal) {
        self.id = UUID()
        self.title = title
        self.description = description
        self.dueDate = dueDate
        self.priority = priority
        self.status = .active
        self.tags = []
        self.estimatedDuration = nil
        self.actualDuration = nil
        self.createdAt = Date()
        self.updatedAt = Date()
        self.completedAt = nil
        self.taskType = taskType
        self.groupId = nil
        self.assignedToUserId = nil
        self.createdByUserId = createdByUserId
        self.recurrence = nil
    }

    // Initializer for Supabase data with existing ID
    init(id: UUID, title: String, description: String? = nil, dueDate: Date? = nil, priority: Priority = .medium, status: TaskStatus = .active, createdByUserId: UUID, taskType: TaskType = .personal, groupId: UUID? = nil, completedAt: Date? = nil, createdAt: Date = Date(), updatedAt: Date = Date()) {
        self.id = id
        self.title = title
        self.description = description
        self.dueDate = dueDate
        self.priority = priority
        self.status = status
        self.tags = []
        self.estimatedDuration = nil
        self.actualDuration = nil
        self.createdAt = createdAt
        self.updatedAt = updatedAt
        self.completedAt = completedAt
        self.taskType = taskType
        self.groupId = groupId
        self.assignedToUserId = nil
        self.createdByUserId = createdByUserId
        self.recurrence = nil
    }
}

// MARK: - Task Enums
extension Task {
    enum Priority: String, Codable, CaseIterable {
        case low = "low"
        case medium = "medium"
        case high = "high"
        
        var displayName: String {
            switch self {
            case .low: return "Low"
            case .medium: return "Medium"
            case .high: return "High"
            }
        }
        
        var sortOrder: Int {
            switch self {
            case .high: return 3
            case .medium: return 2
            case .low: return 1
            }
        }
    }
    
    enum TaskStatus: String, Codable, CaseIterable {
        case draft = "draft"
        case active = "active"
        case inProgress = "in_progress"
        case completed = "completed"
        case cancelled = "cancelled"
        case archived = "archived"

        var displayName: String {
            switch self {
            case .draft: return "Draft"
            case .active: return "Active"
            case .inProgress: return "In Progress"
            case .completed: return "Completed"
            case .cancelled: return "Cancelled"
            case .archived: return "Archived"
            }
        }
    }
    
    enum TaskType: String, Codable {
        case personal = "personal"
        case group = "group"
    }
}

// MARK: - Recurrence Pattern
struct RecurrencePattern: Codable {
    var type: RecurrenceType
    var interval: Int // e.g., every 2 days, every 3 weeks
    var daysOfWeek: [Int]? // 1-7 for Sunday-Saturday
    var endDate: Date?
    
    enum RecurrenceType: String, Codable, CaseIterable {
        case daily = "daily"
        case weekly = "weekly"
        case monthly = "monthly"
        case custom = "custom"
        
        var displayName: String {
            switch self {
            case .daily: return "Daily"
            case .weekly: return "Weekly"
            case .monthly: return "Monthly"
            case .custom: return "Custom"
            }
        }
    }
}

// MARK: - Task Extensions
extension Task {
    // Check if task is overdue
    var isOverdue: Bool {
        guard let dueDate = dueDate, status != .completed else { return false }
        return dueDate < Date()
    }
    
    // Check if task is due today
    var isDueToday: Bool {
        guard let dueDate = dueDate else { return false }
        return Calendar.current.isDateInToday(dueDate)
    }
    
    // Check if task is due this week
    var isDueThisWeek: Bool {
        guard let dueDate = dueDate else { return false }
        let calendar = Calendar.current
        let now = Date()
        let weekFromNow = calendar.date(byAdding: .weekOfYear, value: 1, to: now) ?? now
        return dueDate >= now && dueDate <= weekFromNow
    }
    
    // Get time until due
    var timeUntilDue: String? {
        guard let dueDate = dueDate else { return nil }
        let formatter = RelativeDateTimeFormatter()
        formatter.unitsStyle = .abbreviated
        return formatter.localizedString(for: dueDate, relativeTo: Date())
    }
    
    // Mark task as completed
    mutating func markCompleted() {
        status = .completed
        completedAt = Date()
        updatedAt = Date()
    }
    
    // Mark task as active
    mutating func markActive() {
        status = .active
        completedAt = nil
        updatedAt = Date()
    }
    
    // Update task
    mutating func update(title: String? = nil, description: String? = nil, dueDate: Date? = nil, priority: Priority? = nil, tags: [String]? = nil) {
        if let title = title { self.title = title }
        if let description = description { self.description = description }
        if let dueDate = dueDate { self.dueDate = dueDate }
        if let priority = priority { self.priority = priority }
        if let tags = tags { self.tags = tags }
        self.updatedAt = Date()
    }
    
    // Check if task matches search criteria
    func matchesSearch(_ searchText: String) -> Bool {
        let lowercasedSearch = searchText.lowercased()
        return title.lowercased().contains(lowercasedSearch) ||
               description?.lowercased().contains(lowercasedSearch) == true ||
               tags.contains { $0.lowercased().contains(lowercasedSearch) }
    }
    
    // Check if task matches filter criteria
    func matchesFilter(priority: Priority? = nil, status: TaskStatus? = nil, tags: [String]? = nil, dueDateRange: ClosedRange<Date>? = nil) -> Bool {
        if let filterPriority = priority, self.priority != filterPriority {
            return false
        }
        
        if let filterStatus = status, self.status != filterStatus {
            return false
        }
        
        if let filterTags = tags, !filterTags.isEmpty {
            let hasMatchingTag = filterTags.contains { filterTag in
                self.tags.contains { taskTag in
                    taskTag.lowercased().contains(filterTag.lowercased())
                }
            }
            if !hasMatchingTag { return false }
        }
        
        if let dateRange = dueDateRange, let taskDueDate = dueDate {
            if !dateRange.contains(taskDueDate) {
                return false
            }
        }
        
        return true
    }
}

// MARK: - Sample Data
extension Task {
    static func samplePersonalTasks(for userId: UUID) -> [Task] {
        [
            Task(title: "Review quarterly marketing report", description: "Analyze Q4 performance metrics and prepare insights for team meeting", dueDate: Calendar.current.date(byAdding: .day, value: 1, to: Date()), priority: .high, createdByUserId: userId),
            Task(title: "Morning workout routine", description: "30-minute cardio and strength training", dueDate: Calendar.current.date(byAdding: .hour, value: 2, to: Date()), priority: .medium, createdByUserId: userId),
            Task(title: "Call mom", description: "Weekly check-in call with family", dueDate: Calendar.current.date(byAdding: .day, value: 2, to: Date()), priority: .low, createdByUserId: userId),
            Task(title: "Prepare presentation slides", description: "Create slides for next week's client presentation", dueDate: Calendar.current.date(byAdding: .day, value: 5, to: Date()), priority: .high, createdByUserId: userId)
        ]
    }
    
    static func sampleGroupTasks(for userId: UUID, groupId: UUID) -> [Task] {
        var tasks = [
            Task(title: "Design user interface mockups", description: "Create wireframes and mockups for the new feature", dueDate: Calendar.current.date(byAdding: .day, value: 3, to: Date()), priority: .high, createdByUserId: userId, taskType: .group),
            Task(title: "Research competitor analysis", description: "Analyze top 5 competitors and their features", dueDate: Calendar.current.date(byAdding: .day, value: 7, to: Date()), priority: .medium, createdByUserId: userId, taskType: .group),
            Task(title: "Write project documentation", description: "Document the project requirements and specifications", dueDate: Calendar.current.date(byAdding: .day, value: 10, to: Date()), priority: .medium, createdByUserId: userId, taskType: .group)
        ]
        
        for i in 0..<tasks.count {
            tasks[i].groupId = groupId
        }
        
        return tasks
    }
}
