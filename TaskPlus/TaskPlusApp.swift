//
//  TaskPlusApp.swift
//  TaskPlus
//
//  Created by m<PERSON><PERSON><PERSON> on 14/12/1446 AH.
//

import SwiftUI

@main
struct TaskPlusApp: App {
    // MARK: - State Objects
    @StateObject private var dataManager = DataManager.shared
    @StateObject private var notificationManager = NotificationManager.shared
    @StateObject private var settingsManager = SettingsManager.shared

    var body: some Scene {
        WindowGroup {
            ContentView()
                .environmentObject(dataManager)
                .environmentObject(notificationManager)
                .environmentObject(settingsManager)
                .onAppear {
                    setupApp()
                }
        }
    }

    // MARK: - App Setup
    private func setupApp() {
        // Request notification permissions
        _Concurrency.Task {
            await notificationManager.requestAuthorization()
        }

        // Schedule daily summary if enabled
        if notificationManager.notificationSettings.dailySummaryEnabled {
            notificationManager.scheduleDailySummary()
        }

        // Schedule task reminders
        notificationManager.scheduleAllTaskReminders()

        // Clear badge count on app launch
        notificationManager.clearBadge()
    }
}
