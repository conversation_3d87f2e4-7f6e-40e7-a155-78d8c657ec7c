//
//  DurationPickerView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Duration Picker View
struct DurationPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var duration: TimeInterval
    
    @State private var hours: Int
    @State private var minutes: Int
    
    init(duration: Binding<TimeInterval>) {
        self._duration = duration
        let totalMinutes = Int(duration.wrappedValue / 60)
        self._hours = State(initialValue: totalMinutes / 60)
        self._minutes = State(initialValue: totalMinutes % 60)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 32) {
                // Header
                VStack(spacing: 8) {
                    Text("Estimated Duration")
                        .font(DesignSystem.Typography.title1)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text("How long do you think this task will take?")
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 32)
                
                // Duration Display
                VStack(spacing: 16) {
                    HStack(spacing: 16) {
                        // Hours
                        VStack(spacing: 8) {
                            Text("\(hours)")
                                .font(.system(size: 48, weight: .bold, design: .rounded))
                                .foregroundColor(DesignSystem.Colors.primary)
                            
                            Text("Hours")
                                .font(DesignSystem.Typography.caption)
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                        .frame(maxWidth: .infinity)
                        
                        Text(":")
                            .font(.system(size: 48, weight: .bold))
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        
                        // Minutes
                        VStack(spacing: 8) {
                            Text(String(format: "%02d", minutes))
                                .font(.system(size: 48, weight: .bold, design: .rounded))
                                .foregroundColor(DesignSystem.Colors.primary)
                            
                            Text("Minutes")
                                .font(DesignSystem.Typography.caption)
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                        .frame(maxWidth: .infinity)
                    }
                    .padding(.horizontal, 32)
                    
                    // Total Duration
                    Text("Total: \(formatTotalDuration())")
                        .font(DesignSystem.Typography.headline)
                        .foregroundColor(DesignSystem.Colors.text)
                        .padding(.horizontal, 16)
                        .padding(.vertical, 8)
                        .background(DesignSystem.Colors.surface)
                        .cornerRadius(DesignSystem.CornerRadius.medium)
                }
                
                // Pickers
                VStack(spacing: 24) {
                    // Hours Picker
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Hours")
                            .font(DesignSystem.Typography.bodyBold)
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Picker("Hours", selection: $hours) {
                            ForEach(0...23, id: \.self) { hour in
                                Text("\(hour)").tag(hour)
                            }
                        }
                        .pickerStyle(WheelPickerStyle())
                        .frame(height: 120)
                        .background(DesignSystem.Colors.surface)
                        .cornerRadius(DesignSystem.CornerRadius.medium)
                    }
                    
                    // Minutes Picker
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Minutes")
                            .font(DesignSystem.Typography.bodyBold)
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Picker("Minutes", selection: $minutes) {
                            ForEach(Array(stride(from: 0, through: 59, by: 5)), id: \.self) { minute in
                                Text("\(minute)").tag(minute)
                            }
                        }
                        .pickerStyle(WheelPickerStyle())
                        .frame(height: 120)
                        .background(DesignSystem.Colors.surface)
                        .cornerRadius(DesignSystem.CornerRadius.medium)
                    }
                }
                .padding(.horizontal, 20)
                
                // Quick Duration Buttons
                VStack(alignment: .leading, spacing: 12) {
                    Text("Quick Select")
                        .font(DesignSystem.Typography.bodyBold)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 12) {
                        QuickDurationButton(title: "15 min", hours: 0, minutes: 15, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "30 min", hours: 0, minutes: 30, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "1 hour", hours: 1, minutes: 0, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "2 hours", hours: 2, minutes: 0, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "4 hours", hours: 4, minutes: 0, selectedHours: $hours, selectedMinutes: $minutes)
                        QuickDurationButton(title: "8 hours", hours: 8, minutes: 0, selectedHours: $hours, selectedMinutes: $minutes)
                    }
                }
                .padding(.horizontal, 20)
                
                Spacer()
            }
            .background(DesignSystem.Colors.background)
            .navigationTitle("Duration")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        saveDuration()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    private func formatTotalDuration() -> String {
        let totalMinutes = hours * 60 + minutes
        
        if totalMinutes == 0 {
            return "No duration set"
        } else if hours == 0 {
            return "\(minutes) minutes"
        } else if minutes == 0 {
            return "\(hours) hour\(hours == 1 ? "" : "s")"
        } else {
            return "\(hours)h \(minutes)m"
        }
    }
    
    private func saveDuration() {
        duration = TimeInterval((hours * 60 + minutes) * 60)
    }
}

// MARK: - Quick Duration Button
struct QuickDurationButton: View {
    let title: String
    let hours: Int
    let minutes: Int
    @Binding var selectedHours: Int
    @Binding var selectedMinutes: Int
    
    private var isSelected: Bool {
        selectedHours == hours && selectedMinutes == minutes
    }
    
    var body: some View {
        Button(action: {
            selectedHours = hours
            selectedMinutes = minutes
        }) {
            Text(title)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(isSelected ? .white : DesignSystem.Colors.text)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .background(isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.surface)
                .cornerRadius(DesignSystem.CornerRadius.small)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    DurationPickerView(duration: .constant(3600))
}
