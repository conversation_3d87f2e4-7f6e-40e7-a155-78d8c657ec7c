//
//  RecurrencePickerView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Recurrence Picker View
struct RecurrencePickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var recurrence: RecurrencePattern?
    
    @State private var hasRecurrence = false
    @State private var selectedType: RecurrencePattern.RecurrenceType = .daily
    @State private var interval = 1
    @State private var endDate: Date?
    @State private var hasEndDate = false
    @State private var selectedDaysOfWeek: Set<Int> = []
    
    init(recurrence: Binding<RecurrencePattern?>) {
        self._recurrence = recurrence
        self._hasRecurrence = State(initialValue: recurrence.wrappedValue != nil)
        
        if let pattern = recurrence.wrappedValue {
            self._selectedType = State(initialValue: pattern.type)
            self._interval = State(initialValue: pattern.interval)
            self._endDate = State(initialValue: pattern.endDate)
            self._hasEndDate = State(initialValue: pattern.endDate != nil)
            self._selectedDaysOfWeek = State(initialValue: Set(pattern.daysOfWeek ?? []))
        }
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Text("Repeat Task")
                        .font(DesignSystem.Typography.title1)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text("Set up recurring tasks to save time")
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                .padding(.top, 32)
                
                // Toggle for recurrence
                VStack(spacing: 16) {
                    Toggle(isOn: $hasRecurrence) {
                        HStack {
                            Image(systemName: "repeat")
                                .foregroundColor(DesignSystem.Colors.primary)
                            
                            Text("Repeat Task")
                                .font(DesignSystem.Typography.bodyBold)
                                .foregroundColor(DesignSystem.Colors.text)
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    if hasRecurrence {
                        VStack(spacing: 20) {
                            // Recurrence Type
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Repeat")
                                    .font(DesignSystem.Typography.bodyBold)
                                    .foregroundColor(DesignSystem.Colors.text)
                                
                                Picker("Recurrence Type", selection: $selectedType) {
                                    ForEach(RecurrencePattern.RecurrenceType.allCases, id: \.self) { type in
                                        Text(type.displayName).tag(type)
                                    }
                                }
                                .pickerStyle(SegmentedPickerStyle())
                            }
                            
                            // Interval
                            if selectedType != .custom {
                                VStack(alignment: .leading, spacing: 8) {
                                    Text("Every")
                                        .font(DesignSystem.Typography.bodyBold)
                                        .foregroundColor(DesignSystem.Colors.text)
                                    
                                    HStack {
                                        Stepper(value: $interval, in: 1...30) {
                                            Text("\(interval) \(intervalUnit)")
                                                .font(DesignSystem.Typography.body)
                                                .foregroundColor(DesignSystem.Colors.text)
                                        }
                                    }
                                    .padding(12)
                                    .background(DesignSystem.Colors.surface)
                                    .cornerRadius(DesignSystem.CornerRadius.medium)
                                }
                            }
                            
                            // Days of Week (for weekly)
                            if selectedType == .weekly {
                                daysOfWeekSelector
                            }
                            
                            // End Date
                            VStack(alignment: .leading, spacing: 8) {
                                Toggle(isOn: $hasEndDate) {
                                    Text("End Date")
                                        .font(DesignSystem.Typography.bodyBold)
                                        .foregroundColor(DesignSystem.Colors.text)
                                }
                                
                                if hasEndDate {
                                    DatePicker(
                                        "End Date",
                                        selection: Binding(
                                            get: { endDate ?? Date() },
                                            set: { endDate = $0 }
                                        ),
                                        displayedComponents: [.date]
                                    )
                                    .datePickerStyle(CompactDatePickerStyle())
                                }
                            }
                            
                            // Preview
                            recurrencePreview
                        }
                        .padding(.horizontal, 20)
                    }
                }
                
                Spacer()
            }
            .background(DesignSystem.Colors.background)
            .navigationTitle("Repeat")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        saveRecurrence()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - Days of Week Selector
    private var daysOfWeekSelector: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Days of Week")
                .font(DesignSystem.Typography.bodyBold)
                .foregroundColor(DesignSystem.Colors.text)
            
            HStack {
                ForEach(1...7, id: \.self) { day in
                    Button(action: {
                        if selectedDaysOfWeek.contains(day) {
                            selectedDaysOfWeek.remove(day)
                        } else {
                            selectedDaysOfWeek.insert(day)
                        }
                    }) {
                        Text(dayAbbreviation(for: day))
                            .font(DesignSystem.Typography.caption)
                            .fontWeight(.medium)
                            .foregroundColor(selectedDaysOfWeek.contains(day) ? .white : DesignSystem.Colors.text)
                            .frame(width: 32, height: 32)
                            .background(selectedDaysOfWeek.contains(day) ? DesignSystem.Colors.primary : DesignSystem.Colors.surface)
                            .clipShape(Circle())
                    }
                }
            }
        }
    }
    
    // MARK: - Recurrence Preview
    private var recurrencePreview: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text("Preview")
                .font(DesignSystem.Typography.bodyBold)
                .foregroundColor(DesignSystem.Colors.text)
            
            Text(generatePreviewText())
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .padding(12)
                .background(DesignSystem.Colors.surface)
                .cornerRadius(DesignSystem.CornerRadius.medium)
        }
    }
    
    // MARK: - Helper Properties
    private var intervalUnit: String {
        switch selectedType {
        case .daily:
            return interval == 1 ? "day" : "days"
        case .weekly:
            return interval == 1 ? "week" : "weeks"
        case .monthly:
            return interval == 1 ? "month" : "months"
        case .custom:
            return "custom"
        }
    }
    
    // MARK: - Helper Methods
    private func dayAbbreviation(for day: Int) -> String {
        let days = ["S", "M", "T", "W", "T", "F", "S"]
        return days[day - 1]
    }
    
    private func generatePreviewText() -> String {
        if !hasRecurrence {
            return "Task will not repeat"
        }
        
        switch selectedType {
        case .daily:
            if interval == 1 {
                return "Repeats every day"
            } else {
                return "Repeats every \(interval) days"
            }
        case .weekly:
            let dayNames = selectedDaysOfWeek.sorted().map { dayName(for: $0) }
            let daysText = dayNames.isEmpty ? "selected days" : dayNames.joined(separator: ", ")
            
            if interval == 1 {
                return "Repeats weekly on \(daysText)"
            } else {
                return "Repeats every \(interval) weeks on \(daysText)"
            }
        case .monthly:
            if interval == 1 {
                return "Repeats every month"
            } else {
                return "Repeats every \(interval) months"
            }
        case .custom:
            return "Custom recurrence pattern"
        }
    }
    
    private func dayName(for day: Int) -> String {
        let days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"]
        return days[day - 1]
    }
    
    private func saveRecurrence() {
        if hasRecurrence {
            recurrence = RecurrencePattern(
                type: selectedType,
                interval: interval,
                daysOfWeek: selectedType == .weekly ? Array(selectedDaysOfWeek) : nil,
                endDate: hasEndDate ? endDate : nil
            )
        } else {
            recurrence = nil
        }
    }
}

#Preview {
    RecurrencePickerView(recurrence: .constant(nil))
}
