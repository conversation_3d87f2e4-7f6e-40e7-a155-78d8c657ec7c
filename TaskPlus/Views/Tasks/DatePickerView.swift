//
//  DatePickerView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Date Picker View
struct DatePickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var date: Date?
    let title: String
    let includeTime: Bool
    
    @State private var selectedDate: Date
    @State private var hasDate: Bool
    
    init(date: Binding<Date?>, title: String, includeTime: Bool = false) {
        self._date = date
        self.title = title
        self.includeTime = includeTime
        self._selectedDate = State(initialValue: date.wrappedValue ?? Date())
        self._hasDate = State(initialValue: date.wrappedValue != nil)
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Text(title)
                        .font(DesignSystem.Typography.title1)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    if includeTime {
                        Text("Set a specific date and time")
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    } else {
                        Text("Choose when this task is due")
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
                .padding(.top, 32)
                
                // Toggle for having a date
                VStack(spacing: 16) {
                    Toggle(isOn: $hasDate) {
                        HStack {
                            Image(systemName: includeTime ? "bell" : "calendar")
                                .foregroundColor(DesignSystem.Colors.primary)
                            
                            Text(includeTime ? "Set Reminder" : "Set Due Date")
                                .font(DesignSystem.Typography.bodyBold)
                                .foregroundColor(DesignSystem.Colors.text)
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    if hasDate {
                        // Date Picker
                        DatePicker(
                            "",
                            selection: $selectedDate,
                            displayedComponents: includeTime ? [.date, .hourAndMinute] : [.date]
                        )
                        .datePickerStyle(WheelDatePickerStyle())
                        .labelsHidden()
                        .padding(.horizontal, 20)
                        
                        // Quick Date Options (for due dates only)
                        if !includeTime {
                            quickDateOptions
                        }
                        
                        // Selected Date Display
                        VStack(spacing: 8) {
                            Text("Selected \(includeTime ? "Reminder" : "Due Date")")
                                .font(DesignSystem.Typography.caption)
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                            
                            Text(formatSelectedDate())
                                .font(DesignSystem.Typography.headline)
                                .foregroundColor(DesignSystem.Colors.text)
                                .padding(.horizontal, 16)
                                .padding(.vertical, 8)
                                .background(DesignSystem.Colors.surface)
                                .cornerRadius(DesignSystem.CornerRadius.medium)
                        }
                    }
                }
                
                Spacer()
            }
            .background(DesignSystem.Colors.background)
            .navigationTitle(title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        saveDate()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - Quick Date Options
    private var quickDateOptions: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Select")
                .font(DesignSystem.Typography.bodyBold)
                .foregroundColor(DesignSystem.Colors.text)
                .padding(.horizontal, 20)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                QuickDateButton(title: "Today", date: Calendar.current.startOfDay(for: Date()), selectedDate: $selectedDate)
                QuickDateButton(title: "Tomorrow", date: Calendar.current.date(byAdding: .day, value: 1, to: Date()) ?? Date(), selectedDate: $selectedDate)
                QuickDateButton(title: "This Weekend", date: nextWeekend(), selectedDate: $selectedDate)
                QuickDateButton(title: "Next Week", date: Calendar.current.date(byAdding: .weekOfYear, value: 1, to: Date()) ?? Date(), selectedDate: $selectedDate)
                QuickDateButton(title: "Next Month", date: Calendar.current.date(byAdding: .month, value: 1, to: Date()) ?? Date(), selectedDate: $selectedDate)
                QuickDateButton(title: "No Due Date", date: nil, selectedDate: $selectedDate, isRemoveOption: true)
            }
            .padding(.horizontal, 20)
        }
    }
    
    private func formatSelectedDate() -> String {
        let formatter = DateFormatter()
        
        if includeTime {
            formatter.dateStyle = .medium
            formatter.timeStyle = .short
        } else {
            formatter.dateStyle = .full
        }
        
        return formatter.string(from: selectedDate)
    }
    
    private func saveDate() {
        date = hasDate ? selectedDate : nil
    }
    
    private func nextWeekend() -> Date {
        let calendar = Calendar.current
        let today = Date()
        
        // Find next Saturday
        var components = calendar.dateComponents([.weekday], from: today)
        let daysUntilSaturday = (7 - components.weekday! + 7) % 7
        let adjustedDays = daysUntilSaturday == 0 ? 7 : daysUntilSaturday
        
        return calendar.date(byAdding: .day, value: adjustedDays, to: today) ?? today
    }
}

// MARK: - Quick Date Button
struct QuickDateButton: View {
    let title: String
    let date: Date?
    @Binding var selectedDate: Date
    let isRemoveOption: Bool
    
    init(title: String, date: Date?, selectedDate: Binding<Date>, isRemoveOption: Bool = false) {
        self.title = title
        self.date = date
        self._selectedDate = selectedDate
        self.isRemoveOption = isRemoveOption
    }
    
    private var isSelected: Bool {
        guard let date = date else { return false }
        return Calendar.current.isDate(selectedDate, inSameDayAs: date)
    }
    
    var body: some View {
        Button(action: {
            if let date = date {
                selectedDate = date
            }
        }) {
            VStack(spacing: 4) {
                Text(title)
                    .font(DesignSystem.Typography.caption)
                    .fontWeight(.medium)
                    .foregroundColor(isSelected ? .white : DesignSystem.Colors.text)
                
                if let date = date, !isRemoveOption {
                    Text(formatQuickDate(date))
                        .font(.caption2)
                        .foregroundColor(isSelected ? .white.opacity(0.8) : DesignSystem.Colors.textSecondary)
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .frame(maxWidth: .infinity)
            .background(isSelected ? DesignSystem.Colors.primary : DesignSystem.Colors.surface)
            .cornerRadius(DesignSystem.CornerRadius.small)
        }
        .buttonStyle(PlainButtonStyle())
        .disabled(isRemoveOption)
    }
    
    private func formatQuickDate(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMM d"
        return formatter.string(from: date)
    }
}

#Preview {
    DatePickerView(date: .constant(nil), title: "Due Date")
}

#Preview("With Time") {
    DatePickerView(date: .constant(nil), title: "Reminder", includeTime: true)
}
