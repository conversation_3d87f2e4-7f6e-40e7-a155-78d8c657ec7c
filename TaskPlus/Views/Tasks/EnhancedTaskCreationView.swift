//
//  EnhancedTaskCreationView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Enhanced Task Creation View
struct EnhancedTaskCreationView: View {
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject private var dataManager: DataManager
    @StateObject private var authManager = AuthenticationManager.shared
    
    // MARK: - Form Fields
    @State private var title = ""
    @State private var description = ""
    @State private var selectedPriority: Task.Priority = .medium
    @State private var selectedDifficulty: Task.Difficulty = .medium
    @State private var selectedCategory: TaskCategory?
    @State private var dueDate: Date?
    @State private var reminderTime: Date?
    @State private var estimatedDuration: TimeInterval = 3600 // 1 hour default
    @State private var isImportant = false
    @State private var tags: [String] = []
    @State private var subtasks: [Subtask] = []
    @State private var location: TaskLocation?
    @State private var recurrence: RecurrencePattern?
    
    // MARK: - UI State
    @State private var showingDatePicker = false
    @State private var showingReminderPicker = false
    @State private var showingDurationPicker = false
    @State private var showingLocationPicker = false
    @State private var showingRecurrencePicker = false
    @State private var showingCategoryPicker = false
    @State private var newTagText = ""
    @State private var newSubtaskText = ""
    @State private var isCreating = false
    
    // MARK: - Focus States
    @FocusState private var focusedField: Field?
    
    enum Field {
        case title, description, newTag, newSubtask
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Basic Information Section
                    basicInfoSection
                    
                    // Priority and Difficulty Section
                    priorityDifficultySection
                    
                    // Category Section
                    categorySection
                    
                    // Date and Time Section
                    dateTimeSection
                    
                    // Tags Section
                    tagsSection
                    
                    // Subtasks Section
                    subtasksSection
                    
                    // Location Section
                    locationSection
                    
                    // Recurrence Section
                    recurrenceSection
                    
                    Spacer(minLength: 100)
                }
                .padding(.horizontal, 20)
                .padding(.top, 16)
            }
            .background(DesignSystem.Colors.background)
            .navigationTitle("Create Task")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createTask()
                    }
                    .foregroundColor(DesignSystem.Colors.primary)
                    .fontWeight(.semibold)
                    .disabled(!isFormValid || isCreating)
                }
            }
        }
        .onAppear {
            focusedField = .title
        }
        .sheet(isPresented: $showingDatePicker) {
            DatePickerView(date: $dueDate, title: "Due Date")
        }
        .sheet(isPresented: $showingReminderPicker) {
            DatePickerView(date: $reminderTime, title: "Reminder Time", includeTime: true)
        }
        .sheet(isPresented: $showingDurationPicker) {
            DurationPickerView(duration: $estimatedDuration)
        }
        .sheet(isPresented: $showingCategoryPicker) {
            CategoryPickerView(selectedCategory: $selectedCategory)
        }
        .sheet(isPresented: $showingLocationPicker) {
            LocationPickerView(location: $location)
        }
        .sheet(isPresented: $showingRecurrencePicker) {
            RecurrencePickerView(recurrence: $recurrence)
        }
    }
    
    // MARK: - Basic Information Section
    private var basicInfoSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Basic Information", icon: "info.circle")
            
            VStack(spacing: 12) {
                TaskMateTextField(
                    "Task Title",
                    placeholder: "What needs to be done?",
                    text: $title
                )
                .focused($focusedField, equals: .title)
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Description")
                        .font(DesignSystem.Typography.bodyBold)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    TextEditor(text: $description)
                        .focused($focusedField, equals: .description)
                        .frame(minHeight: 80)
                        .padding(12)
                        .background(DesignSystem.Colors.surface)
                        .cornerRadius(DesignSystem.CornerRadius.medium)
                        .overlay(
                            RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                                .stroke(DesignSystem.Colors.border, lineWidth: 1)
                        )
                }
            }
        }
    }
    
    // MARK: - Priority and Difficulty Section
    private var priorityDifficultySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Priority & Difficulty", icon: "exclamationmark.triangle")
            
            HStack(spacing: 16) {
                VStack(alignment: .leading, spacing: 8) {
                    Text("Priority")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Picker("Priority", selection: $selectedPriority) {
                        ForEach(Task.Priority.allCases, id: \.self) { priority in
                            Text(priority.displayName).tag(priority)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Difficulty")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Picker("Difficulty", selection: $selectedDifficulty) {
                        ForEach(Task.Difficulty.allCases, id: \.self) { difficulty in
                            Text(difficulty.displayName).tag(difficulty)
                        }
                    }
                    .pickerStyle(SegmentedPickerStyle())
                }
            }
            
            // Important Toggle
            HStack {
                Image(systemName: isImportant ? "star.fill" : "star")
                    .foregroundColor(isImportant ? .yellow : DesignSystem.Colors.textSecondary)
                
                Text("Mark as Important")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.text)
                
                Spacer()
                
                Toggle("", isOn: $isImportant)
                    .labelsHidden()
            }
            .padding(12)
            .background(DesignSystem.Colors.surface)
            .cornerRadius(DesignSystem.CornerRadius.medium)
        }
    }
    
    // MARK: - Category Section
    private var categorySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Category", icon: "folder")
            
            Button(action: { showingCategoryPicker = true }) {
                HStack {
                    if let category = selectedCategory {
                        Image(systemName: category.icon)
                            .foregroundColor(Color(category.color))
                        Text(category.name)
                            .foregroundColor(DesignSystem.Colors.text)
                    } else {
                        Image(systemName: "folder")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                        Text("Select Category")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    Spacer()
                    
                    Image(systemName: "chevron.right")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .font(.caption)
                }
                .padding(12)
                .background(DesignSystem.Colors.surface)
                .cornerRadius(DesignSystem.CornerRadius.medium)
            }
        }
    }
    
    // MARK: - Date and Time Section
    private var dateTimeSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Schedule", icon: "calendar")
            
            VStack(spacing: 12) {
                // Due Date
                Button(action: { showingDatePicker = true }) {
                    HStack {
                        Image(systemName: "calendar")
                            .foregroundColor(DesignSystem.Colors.primary)
                        
                        if let dueDate = dueDate {
                            Text("Due: \(dueDate, style: .date)")
                                .foregroundColor(DesignSystem.Colors.text)
                        } else {
                            Text("Set Due Date")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                        
                        Spacer()
                        
                        if dueDate != nil {
                            Button(action: { dueDate = nil }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                        }
                    }
                    .padding(12)
                    .background(DesignSystem.Colors.surface)
                    .cornerRadius(DesignSystem.CornerRadius.medium)
                }
                
                // Reminder Time
                Button(action: { showingReminderPicker = true }) {
                    HStack {
                        Image(systemName: "bell")
                            .foregroundColor(DesignSystem.Colors.secondary)
                        
                        if let reminderTime = reminderTime {
                            Text("Reminder: \(reminderTime, style: .time)")
                                .foregroundColor(DesignSystem.Colors.text)
                        } else {
                            Text("Set Reminder")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                        
                        Spacer()
                        
                        if reminderTime != nil {
                            Button(action: { reminderTime = nil }) {
                                Image(systemName: "xmark.circle.fill")
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                        }
                    }
                    .padding(12)
                    .background(DesignSystem.Colors.surface)
                    .cornerRadius(DesignSystem.CornerRadius.medium)
                }
                
                // Estimated Duration
                Button(action: { showingDurationPicker = true }) {
                    HStack {
                        Image(systemName: "clock")
                            .foregroundColor(DesignSystem.Colors.goldenHour)
                        
                        Text("Duration: \(formatDuration(estimatedDuration))")
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Spacer()
                        
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.caption)
                    }
                    .padding(12)
                    .background(DesignSystem.Colors.surface)
                    .cornerRadius(DesignSystem.CornerRadius.medium)
                }
            }
        }
    }

    // MARK: - Tags Section
    private var tagsSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Tags", icon: "tag")

            // Existing Tags
            if !tags.isEmpty {
                LazyVGrid(columns: [GridItem(.adaptive(minimum: 80))], spacing: 8) {
                    ForEach(tags, id: \.self) { tag in
                        HStack(spacing: 4) {
                            Text(tag)
                                .font(DesignSystem.Typography.caption)
                                .foregroundColor(DesignSystem.Colors.text)

                            Button(action: { removeTag(tag) }) {
                                Image(systemName: "xmark")
                                    .font(.caption2)
                                    .foregroundColor(DesignSystem.Colors.textSecondary)
                            }
                        }
                        .padding(.horizontal, 8)
                        .padding(.vertical, 4)
                        .background(DesignSystem.Colors.primary.opacity(0.1))
                        .cornerRadius(12)
                    }
                }
            }

            // Add New Tag
            HStack {
                TextField("Add tag", text: $newTagText)
                    .focused($focusedField, equals: .newTag)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .onSubmit {
                        addTag()
                    }

                Button("Add", action: addTag)
                    .disabled(newTagText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
        }
    }

    // MARK: - Subtasks Section
    private var subtasksSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Subtasks", icon: "list.bullet")

            // Existing Subtasks
            ForEach(subtasks) { subtask in
                HStack {
                    Button(action: { toggleSubtask(subtask.id) }) {
                        Image(systemName: subtask.isCompleted ? "checkmark.circle.fill" : "circle")
                            .foregroundColor(subtask.isCompleted ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
                    }

                    Text(subtask.title)
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.text)
                        .strikethrough(subtask.isCompleted)

                    Spacer()

                    Button(action: { removeSubtask(subtask.id) }) {
                        Image(systemName: "trash")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.caption)
                    }
                }
                .padding(8)
                .background(DesignSystem.Colors.surface)
                .cornerRadius(DesignSystem.CornerRadius.small)
            }

            // Add New Subtask
            HStack {
                TextField("Add subtask", text: $newSubtaskText)
                    .focused($focusedField, equals: .newSubtask)
                    .textFieldStyle(RoundedBorderTextFieldStyle())
                    .onSubmit {
                        addSubtask()
                    }

                Button("Add", action: addSubtask)
                    .disabled(newSubtaskText.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
            }
        }
    }

    // MARK: - Location Section
    private var locationSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Location", icon: "location")

            Button(action: { showingLocationPicker = true }) {
                HStack {
                    Image(systemName: "location")
                        .foregroundColor(DesignSystem.Colors.primary)

                    if let location = location {
                        Text(location.name)
                            .foregroundColor(DesignSystem.Colors.text)
                    } else {
                        Text("Add Location")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }

                    Spacer()

                    if location != nil {
                        Button(action: { location = nil }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    } else {
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.caption)
                    }
                }
                .padding(12)
                .background(DesignSystem.Colors.surface)
                .cornerRadius(DesignSystem.CornerRadius.medium)
            }
        }
    }

    // MARK: - Recurrence Section
    private var recurrenceSection: some View {
        VStack(alignment: .leading, spacing: 16) {
            SectionHeader(title: "Repeat", icon: "repeat")

            Button(action: { showingRecurrencePicker = true }) {
                HStack {
                    Image(systemName: "repeat")
                        .foregroundColor(DesignSystem.Colors.primary)

                    if let recurrence = recurrence {
                        Text(recurrence.type.displayName)
                            .foregroundColor(DesignSystem.Colors.text)
                    } else {
                        Text("No Repeat")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }

                    Spacer()

                    if recurrence != nil {
                        Button(action: { recurrence = nil }) {
                            Image(systemName: "xmark.circle.fill")
                                .foregroundColor(DesignSystem.Colors.textSecondary)
                        }
                    } else {
                        Image(systemName: "chevron.right")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .font(.caption)
                    }
                }
                .padding(12)
                .background(DesignSystem.Colors.surface)
                .cornerRadius(DesignSystem.CornerRadius.medium)
            }
        }
    }

    // MARK: - Helper Methods
    private var isFormValid: Bool {
        !title.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
    
    private func createTask() {
        guard let currentUser = authManager.currentUser else { return }
        
        isCreating = true
        
        var newTask = Task(
            title: title.trimmingCharacters(in: .whitespacesAndNewlines),
            description: description.isEmpty ? nil : description.trimmingCharacters(in: .whitespacesAndNewlines),
            dueDate: dueDate,
            priority: selectedPriority,
            createdByUserId: currentUser.id
        )
        
        // Set enhanced properties
        newTask.difficulty = selectedDifficulty
        newTask.category = selectedCategory
        newTask.reminderTime = reminderTime
        newTask.isImportant = isImportant
        newTask.tags = tags
        newTask.subtasks = subtasks
        newTask.location = location
        newTask.recurrence = recurrence
        newTask.estimatedDuration = estimatedDuration
        
        _Concurrency.Task {
            await dataManager.addTask(newTask)
            
            await MainActor.run {
                isCreating = false
                dismiss()
            }
        }
    }
    
    private func formatDuration(_ duration: TimeInterval) -> String {
        let hours = Int(duration) / 3600
        let minutes = (Int(duration) % 3600) / 60

        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }

    // MARK: - Tag Management
    private func addTag() {
        let trimmedTag = newTagText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTag.isEmpty && !tags.contains(trimmedTag) else { return }

        tags.append(trimmedTag)
        newTagText = ""
    }

    private func removeTag(_ tag: String) {
        tags.removeAll { $0 == tag }
    }

    // MARK: - Subtask Management
    private func addSubtask() {
        let trimmedTitle = newSubtaskText.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedTitle.isEmpty else { return }

        let newSubtask = Subtask(title: trimmedTitle)
        subtasks.append(newSubtask)
        newSubtaskText = ""
    }

    private func removeSubtask(_ id: UUID) {
        subtasks.removeAll { $0.id == id }
    }

    private func toggleSubtask(_ id: UUID) {
        if let index = subtasks.firstIndex(where: { $0.id == id }) {
            subtasks[index].isCompleted.toggle()
        }
    }
}

// MARK: - Section Header
struct SectionHeader: View {
    let title: String
    let icon: String
    
    var body: some View {
        HStack(spacing: 8) {
            Image(systemName: icon)
                .foregroundColor(DesignSystem.Colors.primary)
                .font(.system(size: 16, weight: .medium))
            
            Text(title)
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.text)
            
            Spacer()
        }
    }
}

#Preview {
    EnhancedTaskCreationView()
        .environmentObject(DataManager.shared)
}
