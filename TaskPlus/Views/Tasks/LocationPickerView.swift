//
//  LocationPickerView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Location Picker View
struct LocationPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var location: TaskLocation?
    
    @State private var locationName = ""
    @State private var locationAddress = ""
    @State private var hasLocation = false
    
    init(location: Binding<TaskLocation?>) {
        self._location = location
        self._hasLocation = State(initialValue: location.wrappedValue != nil)
        self._locationName = State(initialValue: location.wrappedValue?.name ?? "")
        self._locationAddress = State(initialValue: location.wrappedValue?.address ?? "")
    }
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 8) {
                    Text("Task Location")
                        .font(DesignSystem.Typography.title1)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text("Add a location to this task for context or location-based reminders")
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
                .padding(.top, 32)
                .padding(.horizontal, 20)
                
                // Toggle for having a location
                VStack(spacing: 16) {
                    Toggle(isOn: $hasLocation) {
                        HStack {
                            Image(systemName: "location")
                                .foregroundColor(DesignSystem.Colors.primary)
                            
                            Text("Add Location")
                                .font(DesignSystem.Typography.bodyBold)
                                .foregroundColor(DesignSystem.Colors.text)
                        }
                    }
                    .padding(.horizontal, 20)
                    
                    if hasLocation {
                        VStack(spacing: 16) {
                            // Location Name
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Location Name")
                                    .font(DesignSystem.Typography.bodyBold)
                                    .foregroundColor(DesignSystem.Colors.text)
                                
                                TextField("e.g., Office, Home, Gym", text: $locationName)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                            }
                            
                            // Location Address (Optional)
                            VStack(alignment: .leading, spacing: 8) {
                                Text("Address (Optional)")
                                    .font(DesignSystem.Typography.bodyBold)
                                    .foregroundColor(DesignSystem.Colors.text)
                                
                                TextField("e.g., 123 Main St, City", text: $locationAddress)
                                    .textFieldStyle(RoundedBorderTextFieldStyle())
                            }
                            
                            // Quick Location Options
                            quickLocationOptions
                        }
                        .padding(.horizontal, 20)
                    }
                }
                
                Spacer()
            }
            .background(DesignSystem.Colors.background)
            .navigationTitle("Location")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        saveLocation()
                        dismiss()
                    }
                    .fontWeight(.semibold)
                }
            }
        }
    }
    
    // MARK: - Quick Location Options
    private var quickLocationOptions: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text("Quick Select")
                .font(DesignSystem.Typography.bodyBold)
                .foregroundColor(DesignSystem.Colors.text)
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                QuickLocationButton(title: "🏠 Home", locationName: $locationName)
                QuickLocationButton(title: "💼 Office", locationName: $locationName)
                QuickLocationButton(title: "🏪 Store", locationName: $locationName)
                QuickLocationButton(title: "🏋️ Gym", locationName: $locationName)
                QuickLocationButton(title: "🏥 Hospital", locationName: $locationName)
                QuickLocationButton(title: "🏫 School", locationName: $locationName)
            }
        }
    }
    
    private func saveLocation() {
        if hasLocation && !locationName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty {
            location = TaskLocation(
                name: locationName.trimmingCharacters(in: .whitespacesAndNewlines),
                address: locationAddress.isEmpty ? nil : locationAddress.trimmingCharacters(in: .whitespacesAndNewlines)
            )
        } else {
            location = nil
        }
    }
}

// MARK: - Quick Location Button
struct QuickLocationButton: View {
    let title: String
    @Binding var locationName: String
    
    var body: some View {
        Button(action: {
            // Extract the text part after the emoji
            let components = title.components(separatedBy: " ")
            if components.count > 1 {
                locationName = components.dropFirst().joined(separator: " ")
            }
        }) {
            Text(title)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.text)
                .padding(.horizontal, 12)
                .padding(.vertical, 8)
                .frame(maxWidth: .infinity)
                .background(DesignSystem.Colors.surface)
                .cornerRadius(DesignSystem.CornerRadius.small)
        }
        .buttonStyle(PlainButtonStyle())
    }
}

#Preview {
    LocationPickerView(location: .constant(nil))
}
