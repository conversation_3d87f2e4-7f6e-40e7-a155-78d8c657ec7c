//
//  CategoryPickerView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Category Picker View
struct CategoryPickerView: View {
    @Environment(\.dismiss) private var dismiss
    @Binding var selectedCategory: TaskCategory?
    
    @State private var categories = TaskCategory.defaultCategories
    @State private var showingCreateCategory = false
    
    var body: some View {
        NavigationView {
            List {
                // No Category Option
                Button(action: {
                    selectedCategory = nil
                    dismiss()
                }) {
                    HStack {
                        Image(systemName: "xmark.circle")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .frame(width: 24, height: 24)
                        
                        Text("No Category")
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Spacer()
                        
                        if selectedCategory == nil {
                            Image(systemName: "checkmark")
                                .foregroundColor(DesignSystem.Colors.primary)
                        }
                    }
                    .padding(.vertical, 4)
                }
                .buttonStyle(PlainButtonStyle())
                
                // Default Categories
                Section("Categories") {
                    ForEach(categories) { category in
                        Button(action: {
                            selectedCategory = category
                            dismiss()
                        }) {
                            HStack {
                                Image(systemName: category.icon)
                                    .foregroundColor(Color(category.color))
                                    .frame(width: 24, height: 24)
                                
                                Text(category.name)
                                    .foregroundColor(DesignSystem.Colors.text)
                                
                                Spacer()
                                
                                if selectedCategory?.id == category.id {
                                    Image(systemName: "checkmark")
                                        .foregroundColor(DesignSystem.Colors.primary)
                                }
                            }
                            .padding(.vertical, 4)
                        }
                        .buttonStyle(PlainButtonStyle())
                    }
                }
                
                // Create New Category
                Section {
                    Button(action: {
                        showingCreateCategory = true
                    }) {
                        HStack {
                            Image(systemName: "plus.circle")
                                .foregroundColor(DesignSystem.Colors.primary)
                                .frame(width: 24, height: 24)
                            
                            Text("Create New Category")
                                .foregroundColor(DesignSystem.Colors.primary)
                        }
                        .padding(.vertical, 4)
                    }
                    .buttonStyle(PlainButtonStyle())
                }
            }
            .navigationTitle("Select Category")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
        .sheet(isPresented: $showingCreateCategory) {
            CreateCategoryView { newCategory in
                categories.append(newCategory)
                selectedCategory = newCategory
            }
        }
    }
}

// MARK: - Create Category View
struct CreateCategoryView: View {
    @Environment(\.dismiss) private var dismiss
    let onCategoryCreated: (TaskCategory) -> Void
    
    @State private var name = ""
    @State private var selectedColor = "blue"
    @State private var selectedIcon = "folder"
    
    private let availableColors = ["blue", "green", "red", "purple", "yellow", "orange", "teal", "brown", "pink", "indigo"]
    private let availableIcons = ["folder", "briefcase", "person", "heart", "book", "dollarsign.circle", "cart", "airplane", "house", "star", "flag", "bell", "gear", "camera", "music.note"]
    
    var body: some View {
        NavigationView {
            Form {
                Section("Category Details") {
                    TextField("Category Name", text: $name)
                }
                
                Section("Color") {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 5), spacing: 16) {
                        ForEach(availableColors, id: \.self) { color in
                            Button(action: {
                                selectedColor = color
                            }) {
                                Circle()
                                    .fill(Color(color))
                                    .frame(width: 40, height: 40)
                                    .overlay(
                                        Circle()
                                            .stroke(DesignSystem.Colors.primary, lineWidth: selectedColor == color ? 3 : 0)
                                    )
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }
                
                Section("Icon") {
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 6), spacing: 16) {
                        ForEach(availableIcons, id: \.self) { icon in
                            Button(action: {
                                selectedIcon = icon
                            }) {
                                Image(systemName: icon)
                                    .font(.title2)
                                    .foregroundColor(selectedIcon == icon ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
                                    .frame(width: 40, height: 40)
                                    .background(
                                        Circle()
                                            .fill(selectedIcon == icon ? DesignSystem.Colors.primary.opacity(0.1) : Color.clear)
                                    )
                            }
                        }
                    }
                    .padding(.vertical, 8)
                }
                
                Section("Preview") {
                    HStack {
                        Image(systemName: selectedIcon)
                            .foregroundColor(Color(selectedColor))
                            .frame(width: 24, height: 24)
                        
                        Text(name.isEmpty ? "Category Name" : name)
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Spacer()
                    }
                    .padding(.vertical, 8)
                }
            }
            .navigationTitle("New Category")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Create") {
                        createCategory()
                    }
                    .disabled(name.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty)
                }
            }
        }
    }
    
    private func createCategory() {
        let trimmedName = name.trimmingCharacters(in: .whitespacesAndNewlines)
        guard !trimmedName.isEmpty else { return }
        
        let newCategory = TaskCategory(
            name: trimmedName,
            color: selectedColor,
            icon: selectedIcon
        )
        
        onCategoryCreated(newCategory)
        dismiss()
    }
}

#Preview {
    CategoryPickerView(selectedCategory: .constant(nil))
}

#Preview("Create Category") {
    CreateCategoryView { _ in }
}
