//
//  TaskRowView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

// MARK: - Task Row View
struct TaskRowView: View {
    let task: Task
    @EnvironmentObject private var dataManager: DataManager
    @State private var showingTaskDetail = false
    
    var body: some View {
        Button(action: {
            showingTaskDetail = true
        }) {
            HStack(spacing: 12) {
                // Completion Button
                Button(action: {
                    toggleCompletion()
                }) {
                    Image(systemName: task.status == .completed ? "checkmark.circle.fill" : "circle")
                        .font(.title2)
                        .foregroundColor(task.status == .completed ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
                }
                .buttonStyle(PlainButtonStyle())
                
                // Task Content
                VStack(alignment: .leading, spacing: 4) {
                    // Title and Important Star
                    HStack {
                        Text(task.title)
                            .font(DesignSystem.Typography.bodyBold)
                            .foregroundColor(DesignSystem.Colors.text)
                            .strikethrough(task.status == .completed)
                            .lineLimit(2)
                        
                        if task.isImportant {
                            Image(systemName: "star.fill")
                                .font(.caption)
                                .foregroundColor(.yellow)
                        }
                        
                        Spacer()
                    }
                    
                    // Description
                    if let description = task.description, !description.isEmpty {
                        Text(description)
                            .font(DesignSystem.Typography.caption)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .lineLimit(1)
                    }
                    
                    // Tags and Metadata
                    HStack(spacing: 8) {
                        // Priority Badge
                        priorityBadge
                        
                        // Category
                        if let category = task.category {
                            HStack(spacing: 4) {
                                Image(systemName: category.icon)
                                    .font(.caption2)
                                Text(category.name)
                                    .font(.caption2)
                            }
                            .foregroundColor(Color(category.color))
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color(category.color).opacity(0.1))
                            .cornerRadius(4)
                        }
                        
                        // Due Date
                        if let dueDate = task.dueDate {
                            dueDateBadge(dueDate)
                        }
                        
                        Spacer()
                        
                        // Difficulty
                        difficultyBadge
                    }
                    
                    // Subtasks Progress
                    if !task.subtasks.isEmpty {
                        subtasksProgress
                    }
                }
                
                // Chevron
                Image(systemName: "chevron.right")
                    .font(.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
            }
            .padding(.vertical, 8)
        }
        .buttonStyle(PlainButtonStyle())
        .sheet(isPresented: $showingTaskDetail) {
            TaskDetailView(task: task)
        }
    }
    
    // MARK: - Priority Badge
    private var priorityBadge: some View {
        HStack(spacing: 2) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.caption2)
            Text(task.priority.displayName)
                .font(.caption2)
                .fontWeight(.medium)
        }
        .foregroundColor(priorityColor)
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(priorityColor.opacity(0.1))
        .cornerRadius(4)
    }
    
    private var priorityColor: Color {
        switch task.priority {
        case .high:
            return .red
        case .medium:
            return .orange
        case .low:
            return .green
        }
    }
    
    // MARK: - Due Date Badge
    private func dueDateBadge(_ dueDate: Date) -> some View {
        HStack(spacing: 2) {
            Image(systemName: "calendar")
                .font(.caption2)
            Text(formatDueDate(dueDate))
                .font(.caption2)
        }
        .foregroundColor(dueDateColor(dueDate))
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(dueDateColor(dueDate).opacity(0.1))
        .cornerRadius(4)
    }
    
    private func formatDueDate(_ date: Date) -> String {
        let calendar = Calendar.current
        
        if calendar.isDateInToday(date) {
            return "Today"
        } else if calendar.isDateInTomorrow(date) {
            return "Tomorrow"
        } else if calendar.isDate(date, equalTo: Date(), toGranularity: .weekOfYear) {
            let formatter = DateFormatter()
            formatter.dateFormat = "EEEE"
            return formatter.string(from: date)
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "MMM d"
            return formatter.string(from: date)
        }
    }
    
    private func dueDateColor(_ date: Date) -> Color {
        if task.status == .completed {
            return DesignSystem.Colors.textSecondary
        } else if date < Date() {
            return .red
        } else if Calendar.current.isDateInToday(date) {
            return .orange
        } else {
            return DesignSystem.Colors.textSecondary
        }
    }
    
    // MARK: - Difficulty Badge
    private var difficultyBadge: some View {
        HStack(spacing: 2) {
            Image(systemName: "gauge")
                .font(.caption2)
            Text(task.difficulty.displayName)
                .font(.caption2)
        }
        .foregroundColor(Color(task.difficulty.color))
        .padding(.horizontal, 6)
        .padding(.vertical, 2)
        .background(Color(task.difficulty.color).opacity(0.1))
        .cornerRadius(4)
    }
    
    // MARK: - Subtasks Progress
    private var subtasksProgress: some View {
        let completedCount = task.subtasks.filter { $0.isCompleted }.count
        let totalCount = task.subtasks.count
        
        return HStack(spacing: 4) {
            Image(systemName: "list.bullet")
                .font(.caption2)
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Text("\(completedCount)/\(totalCount)")
                .font(.caption2)
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            // Progress Bar
            GeometryReader { geometry in
                ZStack(alignment: .leading) {
                    Rectangle()
                        .fill(DesignSystem.Colors.textSecondary.opacity(0.2))
                        .frame(height: 2)
                    
                    Rectangle()
                        .fill(DesignSystem.Colors.primary)
                        .frame(width: geometry.size.width * (totalCount > 0 ? CGFloat(completedCount) / CGFloat(totalCount) : 0), height: 2)
                }
            }
            .frame(height: 2)
        }
    }
    
    // MARK: - Actions
    private func toggleCompletion() {
        _Concurrency.Task {
            await dataManager.toggleTaskCompletion(task)
        }
    }
}

// MARK: - Task Detail View (Placeholder)
struct TaskDetailView: View {
    let task: Task
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(alignment: .leading, spacing: 20) {
                    // Title
                    Text(task.title)
                        .font(DesignSystem.Typography.title1)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    // Description
                    if let description = task.description {
                        Text(description)
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    // Metadata
                    VStack(alignment: .leading, spacing: 12) {
                        if let dueDate = task.dueDate {
                            HStack {
                                Image(systemName: "calendar")
                                    .foregroundColor(DesignSystem.Colors.primary)
                                Text("Due: \(dueDate, style: .date)")
                                    .font(DesignSystem.Typography.body)
                            }
                        }
                        
                        HStack {
                            Image(systemName: "exclamationmark.triangle")
                                .foregroundColor(DesignSystem.Colors.primary)
                            Text("Priority: \(task.priority.displayName)")
                                .font(DesignSystem.Typography.body)
                        }
                        
                        HStack {
                            Image(systemName: "gauge")
                                .foregroundColor(DesignSystem.Colors.primary)
                            Text("Difficulty: \(task.difficulty.displayName)")
                                .font(DesignSystem.Typography.body)
                        }
                    }
                    
                    // Subtasks
                    if !task.subtasks.isEmpty {
                        VStack(alignment: .leading, spacing: 8) {
                            Text("Subtasks")
                                .font(DesignSystem.Typography.headline)
                                .foregroundColor(DesignSystem.Colors.text)
                            
                            ForEach(task.subtasks) { subtask in
                                HStack {
                                    Image(systemName: subtask.isCompleted ? "checkmark.circle.fill" : "circle")
                                        .foregroundColor(subtask.isCompleted ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
                                    
                                    Text(subtask.title)
                                        .font(DesignSystem.Typography.body)
                                        .strikethrough(subtask.isCompleted)
                                }
                            }
                        }
                    }
                    
                    Spacer()
                }
                .padding(20)
            }
            .navigationTitle("Task Details")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    let sampleTask = Task(
        title: "Sample Task",
        description: "This is a sample task description",
        dueDate: Date(),
        priority: .high,
        createdByUserId: UUID()
    )
    
    return TaskRowView(task: sampleTask)
        .environmentObject(DataManager.shared)
}
