//
//  TaskMateButton.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - TaskMate Button Component
struct TaskMateButton: View {
    let title: String
    let style: ButtonStyle
    let size: ButtonSize
    let action: () -> Void
    
    @State private var isPressed = false
    
    init(_ title: String, style: ButtonStyle = .primary, size: ButtonSize = .medium, action: @escaping () -> Void) {
        self.title = title
        self.style = style
        self.size = size
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: DesignSystem.Spacing.sm) {
                Text(title)
                    .font(size.font)
                    .fontWeight(.semibold)
            }
            .foregroundColor(style.foregroundColor)
            .padding(.horizontal, size.horizontalPadding)
            .padding(.vertical, size.verticalPadding)
            .frame(maxWidth: size.maxWidth)
            .background(style.background)
            .overlay(style.overlay)
            .cornerRadius(size.cornerRadius)
            .taskMateShadow(style.shadow)
            .scaleEffect(isPressed ? 0.95 : 1.0)
            .animation(DesignSystem.Animation.quick, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
}

// MARK: - Button Styles
extension TaskMateButton {
    enum ButtonStyle {
        case primary
        case secondary
        case tertiary
        case destructive
        case success
        
        var foregroundColor: Color {
            switch self {
            case .primary: return DesignSystem.Colors.textInverse
            case .secondary: return DesignSystem.Colors.sunsetCoral
            case .tertiary: return DesignSystem.Colors.textPrimary
            case .destructive: return DesignSystem.Colors.textInverse
            case .success: return DesignSystem.Colors.textInverse
            }
        }
        
        var background: some View {
            SwiftUI.Group {
                switch self {
                case .primary:
                    LinearGradient(
                        colors: [DesignSystem.Colors.sunriseOrange, DesignSystem.Colors.sunsetCoral],
                        startPoint: .leading,
                        endPoint: .trailing
                    )
                case .secondary:
                    DesignSystem.Colors.backgroundCard
                case .tertiary:
                    DesignSystem.Colors.dawnBlue.opacity(0.3)
                case .destructive:
                    DesignSystem.Colors.error
                case .success:
                    DesignSystem.Colors.success
                }
            }
        }
        
        var overlay: some View {
            SwiftUI.Group {
                switch self {
                case .secondary:
                    RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                        .stroke(DesignSystem.Colors.sunsetCoral, lineWidth: 1.5)
                default:
                    EmptyView()
                }
            }
        }
        
        var shadow: DesignSystem.Shadow {
            switch self {
            case .primary, .destructive, .success:
                return DesignSystem.Shadows.medium
            case .secondary, .tertiary:
                return DesignSystem.Shadows.small
            }
        }
    }
}

// MARK: - Button Sizes
extension TaskMateButton {
    enum ButtonSize {
        case small
        case medium
        case large
        case extraLarge
        
        var font: Font {
            switch self {
            case .small: return DesignSystem.Typography.bodySmallStyle
            case .medium: return DesignSystem.Typography.bodyMediumStyle
            case .large: return DesignSystem.Typography.bodyLargeStyle
            case .extraLarge: return DesignSystem.Typography.headlineSmallStyle
            }
        }
        
        var horizontalPadding: CGFloat {
            switch self {
            case .small: return DesignSystem.Spacing.md
            case .medium: return DesignSystem.Spacing.lg
            case .large: return DesignSystem.Spacing.xl
            case .extraLarge: return DesignSystem.Spacing.xl
            }
        }
        
        var verticalPadding: CGFloat {
            switch self {
            case .small: return DesignSystem.Spacing.sm
            case .medium: return DesignSystem.Spacing.md
            case .large: return DesignSystem.Spacing.lg
            case .extraLarge: return DesignSystem.Spacing.lg
            }
        }
        
        var cornerRadius: CGFloat {
            switch self {
            case .small: return DesignSystem.CornerRadius.small
            case .medium: return DesignSystem.CornerRadius.medium
            case .large: return DesignSystem.CornerRadius.large
            case .extraLarge: return DesignSystem.CornerRadius.large
            }
        }
        
        var maxWidth: CGFloat? {
            switch self {
            case .small, .medium: return nil
            case .large, .extraLarge: return .infinity
            }
        }
    }
}

// MARK: - Icon Button
struct TaskMateIconButton: View {
    let icon: String
    let style: TaskMateButton.ButtonStyle
    let size: IconButtonSize
    let action: () -> Void
    
    @State private var isPressed = false
    
    init(icon: String, style: TaskMateButton.ButtonStyle = .primary, size: IconButtonSize = .medium, action: @escaping () -> Void) {
        self.icon = icon
        self.style = style
        self.size = size
        self.action = action
    }
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: size.iconSize, weight: .semibold))
                .foregroundColor(style.foregroundColor)
                .frame(width: size.frameSize, height: size.frameSize)
                .background(style.background)
                .overlay(style.overlay)
                .cornerRadius(size.cornerRadius)
                .taskMateShadow(style.shadow)
                .scaleEffect(isPressed ? 0.9 : 1.0)
                .animation(DesignSystem.Animation.quick, value: isPressed)
        }
        .buttonStyle(PlainButtonStyle())
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            isPressed = pressing
        }, perform: {})
    }
    
    enum IconButtonSize {
        case small
        case medium
        case large
        
        var iconSize: CGFloat {
            switch self {
            case .small: return 16
            case .medium: return 20
            case .large: return 24
            }
        }
        
        var frameSize: CGFloat {
            switch self {
            case .small: return 32
            case .medium: return 44
            case .large: return 56
            }
        }
        
        var cornerRadius: CGFloat {
            switch self {
            case .small: return DesignSystem.CornerRadius.small
            case .medium: return DesignSystem.CornerRadius.medium
            case .large: return DesignSystem.CornerRadius.large
            }
        }
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: DesignSystem.Spacing.lg) {
        // Button styles
        TaskMateButton("Primary Button", style: .primary) {}
        TaskMateButton("Secondary Button", style: .secondary) {}
        TaskMateButton("Tertiary Button", style: .tertiary) {}
        TaskMateButton("Success Button", style: .success) {}
        TaskMateButton("Destructive Button", style: .destructive) {}
        
        Divider()
        
        // Button sizes
        TaskMateButton("Small", style: .primary, size: .small) {}
        TaskMateButton("Medium", style: .primary, size: .medium) {}
        TaskMateButton("Large", style: .primary, size: .large) {}
        TaskMateButton("Extra Large", style: .primary, size: .extraLarge) {}
        
        Divider()
        
        // Icon buttons
        HStack(spacing: DesignSystem.Spacing.md) {
            TaskMateIconButton(icon: "plus", style: .primary, size: .small) {}
            TaskMateIconButton(icon: "heart", style: .secondary, size: .medium) {}
            TaskMateIconButton(icon: "gear", style: .tertiary, size: .large) {}
        }
    }
    .padding(DesignSystem.Spacing.lg)
}
