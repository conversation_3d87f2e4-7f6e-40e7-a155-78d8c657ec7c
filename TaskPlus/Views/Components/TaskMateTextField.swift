//
//  TaskMateTextField.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

// MARK: - TaskMate Text Field Component
struct TaskMateTextField: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let style: TextFieldStyle
    let keyboardType: UIKeyboardType
    let isSecure: Bool
    let maxLength: Int?
    
    @FocusState private var isFocused: Bool
    @State private var isValid = true
    
    init(
        _ title: String = "",
        placeholder: String,
        text: Binding<String>,
        style: TextFieldStyle = .default,
        keyboardType: UIKeyboardType = .default,
        isSecure: Bool = false,
        maxLength: Int? = nil
    ) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.style = style
        self.keyboardType = keyboardType
        self.isSecure = isSecure
        self.maxLength = maxLength
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            // Title
            if !title.isEmpty {
                Text(title)
                    .font(DesignSystem.Typography.bodyMediumStyle)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            
            // Text Field
            Group {
                if isSecure {
                    SecureField(placeholder, text: $text)
                        .textFieldStyle()
                } else {
                    TextField(placeholder, text: $text)
                        .textFieldStyle()
                }
            }
            .focused($isFocused)
            .keyboardType(keyboardType)
            .onChange(of: text) { newValue in
                // Apply max length if specified
                if let maxLength = maxLength, newValue.count > maxLength {
                    text = String(newValue.prefix(maxLength))
                }
                
                // Validate input
                validateInput()
            }
            
            // Character count (if max length is specified)
            if let maxLength = maxLength {
                HStack {
                    Spacer()
                    Text("\(text.count)/\(maxLength)")
                        .font(DesignSystem.Typography.captionSmallStyle)
                        .foregroundColor(text.count >= maxLength ? DesignSystem.Colors.warning : DesignSystem.Colors.textTertiary)
                }
            }
        }
    }
    
    private func validateInput() {
        // Basic validation - can be extended
        isValid = !text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
    }
}

// MARK: - Text Field Style Extension
private extension View {
    func textFieldStyle() -> some View {
        self
            .font(DesignSystem.Typography.bodyLargeStyle)
            .padding(DesignSystem.Spacing.md)
            .background(DesignSystem.Colors.dawnBlue.opacity(0.2))
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(DesignSystem.Colors.sunriseOrange.opacity(0.3), lineWidth: 1)
            )
    }
}

// MARK: - Text Field Styles
extension TaskMateTextField {
    enum TextFieldStyle {
        case `default`
        case outlined
        case filled
        
        var backgroundColor: Color {
            switch self {
            case .default: return DesignSystem.Colors.dawnBlue.opacity(0.2)
            case .outlined: return Color.clear
            case .filled: return DesignSystem.Colors.warmGray
            }
        }
        
        var borderColor: Color {
            switch self {
            case .default: return DesignSystem.Colors.sunriseOrange.opacity(0.3)
            case .outlined: return DesignSystem.Colors.sunsetCoral
            case .filled: return Color.clear
            }
        }
    }
}

// MARK: - Multi-line Text Field
struct TaskMateTextEditor: View {
    let title: String
    let placeholder: String
    @Binding var text: String
    let minHeight: CGFloat
    let maxLength: Int?
    
    @FocusState private var isFocused: Bool
    
    init(
        _ title: String = "",
        placeholder: String,
        text: Binding<String>,
        minHeight: CGFloat = 100,
        maxLength: Int? = nil
    ) {
        self.title = title
        self.placeholder = placeholder
        self._text = text
        self.minHeight = minHeight
        self.maxLength = maxLength
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: DesignSystem.Spacing.sm) {
            // Title
            if !title.isEmpty {
                Text(title)
                    .font(DesignSystem.Typography.bodyMediumStyle)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
            }
            
            // Text Editor
            ZStack(alignment: .topLeading) {
                // Placeholder
                if text.isEmpty {
                    Text(placeholder)
                        .font(DesignSystem.Typography.bodyLargeStyle)
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                        .padding(.top, DesignSystem.Spacing.md)
                        .padding(.leading, DesignSystem.Spacing.md)
                }
                
                TextEditor(text: $text)
                    .font(DesignSystem.Typography.bodyLargeStyle)
                    .padding(DesignSystem.Spacing.md)
                    .frame(minHeight: minHeight)
                    .focused($isFocused)
                    .onChange(of: text) { newValue in
                        // Apply max length if specified
                        if let maxLength = maxLength, newValue.count > maxLength {
                            text = String(newValue.prefix(maxLength))
                        }
                    }
            }
            .background(DesignSystem.Colors.dawnBlue.opacity(0.2))
            .cornerRadius(DesignSystem.CornerRadius.medium)
            .overlay(
                RoundedRectangle(cornerRadius: DesignSystem.CornerRadius.medium)
                    .stroke(
                        isFocused ? DesignSystem.Colors.sunsetCoral : DesignSystem.Colors.sunriseOrange.opacity(0.3),
                        lineWidth: isFocused ? 2 : 1
                    )
            )
            
            // Character count (if max length is specified)
            if let maxLength = maxLength {
                HStack {
                    Spacer()
                    Text("\(text.count)/\(maxLength)")
                        .font(DesignSystem.Typography.captionSmallStyle)
                        .foregroundColor(text.count >= maxLength ? DesignSystem.Colors.warning : DesignSystem.Colors.textTertiary)
                }
            }
        }
    }
}

// MARK: - Search Field
struct TaskMateSearchField: View {
    @Binding var searchText: String
    let placeholder: String
    let onSearchChanged: ((String) -> Void)?
    
    @FocusState private var isFocused: Bool
    
    init(
        searchText: Binding<String>,
        placeholder: String = "Search...",
        onSearchChanged: ((String) -> Void)? = nil
    ) {
        self._searchText = searchText
        self.placeholder = placeholder
        self.onSearchChanged = onSearchChanged
    }
    
    var body: some View {
        HStack(spacing: DesignSystem.Spacing.sm) {
            Image(systemName: "magnifyingglass")
                .foregroundColor(DesignSystem.Colors.textTertiary)
            
            TextField(placeholder, text: $searchText)
                .font(DesignSystem.Typography.bodyMediumStyle)
                .focused($isFocused)
                .onChange(of: searchText) { newValue in
                    onSearchChanged?(newValue)
                }
            
            if !searchText.isEmpty {
                Button {
                    searchText = ""
                    onSearchChanged?("")
                } label: {
                    Image(systemName: "xmark.circle.fill")
                        .foregroundColor(DesignSystem.Colors.textTertiary)
                }
            }
        }
        .padding(DesignSystem.Spacing.md)
        .background(DesignSystem.Colors.warmGray)
        .cornerRadius(DesignSystem.CornerRadius.medium)
    }
}

// MARK: - Preview
#Preview {
    VStack(spacing: DesignSystem.Spacing.lg) {
        TaskMateTextField(
            "Email",
            placeholder: "Enter your email",
            text: .constant(""),
            keyboardType: .emailAddress
        )
        
        TaskMateTextField(
            "Password",
            placeholder: "Enter your password",
            text: .constant(""),
            isSecure: true
        )
        
        TaskMateTextField(
            "Username",
            placeholder: "Choose a username",
            text: .constant(""),
            maxLength: 20
        )
        
        TaskMateTextEditor(
            "Description",
            placeholder: "Enter a description...",
            text: .constant(""),
            maxLength: 500
        )
        
        TaskMateSearchField(
            searchText: .constant(""),
            placeholder: "Search tasks..."
        )
    }
    .padding(DesignSystem.Spacing.lg)
}
