//
//  ProfileSetupView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI
import PhotosUI

struct ProfileSetupView: View {
    @StateObject private var authManager = AuthenticationManager.shared
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Form Fields
    @State private var username = ""
    @State private var bio = ""
    @State private var selectedPrivacy: PrivacySettings.ProfileVisibility = .friends
    
    // MARK: - Avatar Selection
    @State private var selectedPhoto: PhotosPickerItem?
    @State private var avatarImage: UIImage?
    @State private var showingImagePicker = false
    
    // MARK: - UI State
    @State private var isUsernameAvailable = true
    @State private var isCheckingUsername = false
    @FocusState private var focusedField: Field?
    
    enum Field {
        case username, bio
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Avatar Selection
                    avatarSection
                    
                    // Profile Form
                    profileForm
                    
                    // Privacy Settings
                    privacySection
                    
                    // Complete Button
                    completeButton
                    
                    Spacer(minLength: 20)
                }
                .padding(.horizontal, 24)
                .padding(.top, 20)
            }
            .background(DesignSystem.Colors.background)
            .navigationTitle("Complete Profile")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("Skip") {
                        completeSetup()
                    }
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                }
            }
        }
        .photosPicker(isPresented: $showingImagePicker, selection: $selectedPhoto, matching: .images)
        .onChange(of: selectedPhoto) { _, newValue in
            loadSelectedPhoto(newValue)
        }
        .onChange(of: username) { _, newValue in
            checkUsernameAvailability(newValue)
        }
        .alert("Profile Setup Error", isPresented: .constant(authManager.errorMessage != nil)) {
            Button("OK") {
                authManager.clearError()
            }
        } message: {
            Text(authManager.errorMessage ?? "")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            Image(systemName: "person.crop.circle.badge.plus")
                .font(.system(size: 60))
                .foregroundColor(DesignSystem.Colors.primary)
            
            VStack(spacing: 8) {
                Text("Set Up Your Profile")
                    .font(DesignSystem.Typography.title1)
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("Help your friends recognize you by completing your profile")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    // MARK: - Avatar Section
    private var avatarSection: some View {
        VStack(spacing: 16) {
            Text("Profile Photo")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.text)
            
            Button(action: { showingImagePicker = true }) {
                ZStack {
                    if let avatarImage = avatarImage {
                        Image(uiImage: avatarImage)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 120, height: 120)
                            .clipShape(Circle())
                    } else {
                        Circle()
                            .fill(DesignSystem.Colors.surface)
                            .frame(width: 120, height: 120)
                            .overlay(
                                VStack(spacing: 8) {
                                    Image(systemName: "camera.fill")
                                        .font(.title2)
                                        .foregroundColor(DesignSystem.Colors.primary)
                                    
                                    Text("Add Photo")
                                        .font(DesignSystem.Typography.caption)
                                        .foregroundColor(DesignSystem.Colors.textSecondary)
                                }
                            )
                    }
                    
                    // Edit overlay
                    if avatarImage != nil {
                        Circle()
                            .fill(Color.black.opacity(0.3))
                            .frame(width: 120, height: 120)
                            .overlay(
                                Image(systemName: "camera.fill")
                                    .font(.title2)
                                    .foregroundColor(.white)
                            )
                    }
                }
            }
            .buttonStyle(PlainButtonStyle())
            
            Text("Optional - You can add this later")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textSecondary)
        }
    }
    
    // MARK: - Profile Form
    private var profileForm: some View {
        VStack(spacing: 20) {
            // Username Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Username")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                HStack {
                    TextField("Choose a unique username", text: $username)
                        .focused($focusedField, equals: .username)
                        .autocapitalization(.none)
                        .autocorrectionDisabled()
                    
                    if isCheckingUsername {
                        ProgressView()
                            .scaleEffect(0.8)
                    } else if !username.isEmpty {
                        Image(systemName: isUsernameAvailable ? "checkmark.circle.fill" : "xmark.circle.fill")
                            .foregroundColor(isUsernameAvailable ? .green : .red)
                    }
                }
                .padding()
                .background(DesignSystem.Colors.surface)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(focusedField == .username ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
                )
                
                if !username.isEmpty && !isUsernameAvailable {
                    Text("This username is already taken")
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(.red)
                }
            }
            
            // Bio Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Bio (Optional)")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                TextField("Tell us a bit about yourself", text: $bio, axis: .vertical)
                    .focused($focusedField, equals: .bio)
                    .lineLimit(3...6)
                    .padding()
                    .background(DesignSystem.Colors.surface)
                    .cornerRadius(12)
                    .overlay(
                        RoundedRectangle(cornerRadius: 12)
                            .stroke(focusedField == .bio ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
                    )
                
                Text("\(bio.count)/150 characters")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .frame(maxWidth: .infinity, alignment: .trailing)
            }
        }
    }
    
    // MARK: - Privacy Section
    private var privacySection: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("Privacy Settings")
                .font(DesignSystem.Typography.headline)
                .foregroundColor(DesignSystem.Colors.text)
            
            VStack(spacing: 12) {
                privacyOption(.everyone, "Public", "Anyone can find and view your profile")
                privacyOption(.friends, "Friends Only", "Only your friends can see your profile")
                privacyOption(.privateProfile, "Private", "Your profile is completely private")
            }
        }
    }
    
    private func privacyOption(_ privacy: PrivacySettings.ProfileVisibility, _ title: String, _ description: String) -> some View {
        Button(action: { selectedPrivacy = privacy }) {
            HStack(spacing: 16) {
                Image(systemName: selectedPrivacy == privacy ? "checkmark.circle.fill" : "circle")
                    .foregroundColor(selectedPrivacy == privacy ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
                    .font(.title3)
                
                VStack(alignment: .leading, spacing: 4) {
                    Text(title)
                        .font(DesignSystem.Typography.bodyBold)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text(description)
                        .font(DesignSystem.Typography.caption)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                }
                
                Spacer()
            }
            .padding()
            .background(DesignSystem.Colors.surface)
            .cornerRadius(12)
            .overlay(
                RoundedRectangle(cornerRadius: 12)
                    .stroke(selectedPrivacy == privacy ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
            )
        }
        .buttonStyle(PlainButtonStyle())
    }
    
    // MARK: - Complete Button
    private var completeButton: some View {
        TaskMateButton(
            title: authManager.isLoading ? "Completing..." : "Complete Profile",
            style: .primary,
            isLoading: authManager.isLoading
        ) {
            _Concurrency.Task {
                await handleProfileCompletion()
            }
        }
        .disabled(!isFormValid || authManager.isLoading)
    }
    
    // MARK: - Form Validation
    private var isFormValid: Bool {
        !username.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        isUsernameAvailable &&
        bio.count <= 150
    }
    
    // MARK: - Actions
    private func loadSelectedPhoto(_ item: PhotosPickerItem?) {
        guard let item = item else { return }
        
        _Concurrency.Task {
            if let data = try? await item.loadTransferable(type: Data.self),
               let image = UIImage(data: data) {
                await MainActor.run {
                    avatarImage = image
                }
            }
        }
    }
    
    private func checkUsernameAvailability(_ username: String) {
        guard !username.isEmpty else {
            isUsernameAvailable = true
            return
        }
        
        isCheckingUsername = true
        
        // Simulate API call to check username availability
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // Simulate some usernames being taken
            let takenUsernames = ["admin", "test", "user", "taskmate"]
            isUsernameAvailable = !takenUsernames.contains(username.lowercased())
            isCheckingUsername = false
        }
    }
    
    private func handleProfileCompletion() async {
        guard let currentUser = authManager.currentUser else { return }

        // Create updated user with new profile information
        var updatedUser = currentUser
        updatedUser.username = username.trimmingCharacters(in: .whitespacesAndNewlines)
        updatedUser.bio = bio.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty ? nil : bio.trimmingCharacters(in: .whitespacesAndNewlines)
        updatedUser.privacySettings.profileVisibility = selectedPrivacy
        updatedUser.updatedAt = Date()
        // TODO: Upload avatar image and set avatarURL

        let success = await authManager.updateProfile(user: updatedUser)

        if success {
            completeSetup()
        }
    }
    
    private func completeSetup() {
        dismiss()
    }
}

// MARK: - Preview
struct ProfileSetupView_Previews: PreviewProvider {
    static var previews: some View {
        ProfileSetupView()
    }
}
