//
//  LoginView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

struct LoginView: View {
    @StateObject private var authManager = AuthenticationManager.shared
    
    // MARK: - Form Fields
    @State private var email = ""
    @State private var password = ""
    
    // MARK: - UI State
    @State private var showPassword = false
    @State private var showRegistration = false
    @State private var showForgotPassword = false
    @FocusState private var focusedField: Field?
    
    enum Field {
        case email, password
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 32) {
                    // Header
                    headerSection
                    
                    // Login Form
                    loginForm
                    
                    // Login Button
                    loginButton
                    
                    // Forgot Password
                    forgotPasswordLink
                    
                    // Divider
                    dividerSection
                    
                    // Registration Link
                    registrationLink
                    
                    Spacer(minLength: 20)
                }
                .padding(.horizontal, 24)
                .padding(.top, 40)
            }
            .background(DesignSystem.Colors.background)
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showRegistration) {
            RegistrationView()
        }
        .sheet(isPresented: $showForgotPassword) {
            ForgotPasswordView()
        }
        .alert("Login Error", isPresented: .constant(authManager.errorMessage != nil)) {
            Button("OK") {
                authManager.clearError()
            }
        } message: {
            Text(authManager.errorMessage ?? "")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 20) {
            // App Icon/Logo
            VStack(spacing: 16) {
                Image(systemName: "sun.max.fill")
                    .font(.system(size: 80))
                    .foregroundColor(DesignSystem.Colors.primary)
                    .shadow(color: DesignSystem.Colors.primary.opacity(0.3), radius: 10, x: 0, y: 5)
                
                VStack(spacing: 8) {
                    Text("TaskMate")
                        .font(DesignSystem.Typography.largeTitle)
                        .foregroundColor(DesignSystem.Colors.text)
                    
                    Text("Your social productivity companion")
                        .font(DesignSystem.Typography.body)
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                        .multilineTextAlignment(.center)
                }
            }
            
            // Welcome Message
            VStack(spacing: 8) {
                Text("Welcome Back!")
                    .font(DesignSystem.Typography.title2)
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("Sign in to continue your productivity journey")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    // MARK: - Login Form
    private var loginForm: some View {
        VStack(spacing: 20) {
            // Email Field
            TaskMateTextField(
                "Email",
                placeholder: "Enter your email",
                text: $email,
                keyboardType: .emailAddress
            )
            .focused($focusedField, equals: .email)
            .textContentType(.emailAddress)
            .keyboardType(.emailAddress)
            .autocapitalization(.none)
            .autocorrectionDisabled()
            
            // Password Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Password")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                HStack {
                    if showPassword {
                        TextField("Enter your password", text: $password)
                            .focused($focusedField, equals: .password)
                    } else {
                        SecureField("Enter your password", text: $password)
                            .focused($focusedField, equals: .password)
                    }
                    
                    Button(action: { showPassword.toggle() }) {
                        Image(systemName: showPassword ? "eye.slash" : "eye")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
                .padding()
                .background(DesignSystem.Colors.surface)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(focusedField == .password ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
                )
            }
        }
    }
    
    // MARK: - Login Button
    private var loginButton: some View {
        TaskMateButton(
            authManager.isLoading ? "Signing In..." : "Sign In",
            style: .primary,
            isLoading: authManager.isLoading
        ) {
            _Concurrency.Task {
                await handleLogin()
            }
        }
        .disabled(!isFormValid || authManager.isLoading)
    }
    
    // MARK: - Forgot Password Link
    private var forgotPasswordLink: some View {
        Button("Forgot Password?") {
            showForgotPassword = true
        }
        .font(DesignSystem.Typography.body)
        .foregroundColor(DesignSystem.Colors.primary)
    }
    
    // MARK: - Divider Section
    private var dividerSection: some View {
        HStack {
            Rectangle()
                .frame(height: 1)
                .foregroundColor(DesignSystem.Colors.border)
            
            Text("OR")
                .font(DesignSystem.Typography.caption)
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .padding(.horizontal, 16)
            
            Rectangle()
                .frame(height: 1)
                .foregroundColor(DesignSystem.Colors.border)
        }
    }
    
    // MARK: - Registration Link
    private var registrationLink: some View {
        VStack(spacing: 16) {
            Text("Don't have an account?")
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            TaskMateButton(
                "Create Account",
                style: .secondary
            ) {
                showRegistration = true
            }
        }
    }
    
    // MARK: - Form Validation
    private var isFormValid: Bool {
        !email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !password.isEmpty
    }
    
    // MARK: - Actions
    private func handleLogin() async {
        let success = await authManager.login(
            email: email.trimmingCharacters(in: .whitespacesAndNewlines),
            password: password
        )
        
        if success {
            // Login successful - AuthenticationManager will handle state update
        }
    }
}

// MARK: - Forgot Password View
struct ForgotPasswordView: View {
    @StateObject private var authManager = AuthenticationManager.shared
    @Environment(\.dismiss) private var dismiss
    
    @State private var email = ""
    @State private var showSuccessMessage = false
    
    var body: some View {
        NavigationView {
            VStack(spacing: 24) {
                // Header
                VStack(spacing: 16) {
                    Image(systemName: "lock.rotation")
                        .font(.system(size: 60))
                        .foregroundColor(DesignSystem.Colors.primary)
                    
                    VStack(spacing: 8) {
                        Text("Reset Password")
                            .font(DesignSystem.Typography.title1)
                            .foregroundColor(DesignSystem.Colors.text)
                        
                        Text("Enter your email address and we'll send you a link to reset your password")
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                    }
                }
                .padding(.top, 40)
                
                // Email Field
                TaskMateTextField(
                    "Email",
                    placeholder: "Enter your email",
                    text: $email,
                    keyboardType: .emailAddress
                )
                .textContentType(.emailAddress)
                .keyboardType(.emailAddress)
                .autocapitalization(.none)
                .autocorrectionDisabled()
                
                // Reset Button
                TaskMateButton(
                    authManager.isLoading ? "Sending..." : "Send Reset Link",
                    style: .primary,
                    isLoading: authManager.isLoading
                ) {
                    _Concurrency.Task {
                        await handlePasswordReset()
                    }
                }
                .disabled(email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty || authManager.isLoading)
                
                Spacer()
            }
            .padding(.horizontal, 24)
            .background(DesignSystem.Colors.background)
            .navigationTitle("Reset Password")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("Cancel") {
                        dismiss()
                    }
                }
            }
        }
        .alert("Reset Link Sent", isPresented: $showSuccessMessage) {
            Button("OK") {
                dismiss()
            }
        } message: {
            Text("We've sent a password reset link to your email address.")
        }
        .alert("Error", isPresented: .constant(authManager.errorMessage != nil)) {
            Button("OK") {
                authManager.clearError()
            }
        } message: {
            Text(authManager.errorMessage ?? "")
        }
    }
    
    private func handlePasswordReset() async {
        let success = await authManager.resetPassword(email: email.trimmingCharacters(in: .whitespacesAndNewlines))
        
        if success {
            showSuccessMessage = true
        }
    }
}

// MARK: - Preview
struct LoginView_Previews: PreviewProvider {
    static var previews: some View {
        LoginView()
    }
}

struct ForgotPasswordView_Previews: PreviewProvider {
    static var previews: some View {
        ForgotPasswordView()
    }
}
