//
//  RegistrationView.swift
//  TaskPlus
//
//  Created by TaskMate Team on 2024.
//

import SwiftUI

struct RegistrationView: View {
    @StateObject private var authManager = AuthenticationManager.shared
    @Environment(\.dismiss) private var dismiss
    
    // MARK: - Form Fields
    @State private var firstName = ""
    @State private var lastName = ""
    @State private var email = ""
    @State private var password = ""
    @State private var confirmPassword = ""
    
    // MARK: - UI State
    @State private var showPassword = false
    @State private var showConfirmPassword = false
    @State private var agreedToTerms = false
    @FocusState private var focusedField: Field?
    
    enum Field {
        case firstName, lastName, email, password, confirmPassword
    }
    
    var body: some View {
        NavigationView {
            ScrollView {
                VStack(spacing: 24) {
                    // Header
                    headerSection
                    
                    // Registration Form
                    registrationForm
                    
                    // Terms and Conditions
                    termsSection
                    
                    // Register Button
                    registerButton
                    
                    // Login Link
                    loginLink
                    
                    Spacer(minLength: 20)
                }
                .padding(.horizontal, 24)
                .padding(.top, 20)
            }
            .background(DesignSystem.Colors.background)
            .navigationBarHidden(true)
        }
        .alert("Registration Error", isPresented: .constant(authManager.errorMessage != nil)) {
            Button("OK") {
                authManager.clearError()
            }
        } message: {
            Text(authManager.errorMessage ?? "")
        }
    }
    
    // MARK: - Header Section
    private var headerSection: some View {
        VStack(spacing: 16) {
            // App Icon/Logo
            Image(systemName: "sun.max.fill")
                .font(.system(size: 60))
                .foregroundColor(DesignSystem.Colors.primary)
            
            VStack(spacing: 8) {
                Text("Welcome to TaskMate")
                    .font(DesignSystem.Typography.title1)
                    .foregroundColor(DesignSystem.Colors.text)
                
                Text("Create your account to start managing tasks with friends")
                    .font(DesignSystem.Typography.body)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
            }
        }
    }
    
    // MARK: - Registration Form
    private var registrationForm: some View {
        VStack(spacing: 16) {
            // Name Fields
            HStack(spacing: 12) {
                TaskMateTextField(
                    title: "First Name",
                    text: $firstName,
                    placeholder: "John"
                )
                .focused($focusedField, equals: .firstName)
                .textContentType(.givenName)
                .autocapitalization(.words)
                
                TaskMateTextField(
                    title: "Last Name",
                    text: $lastName,
                    placeholder: "Doe"
                )
                .focused($focusedField, equals: .lastName)
                .textContentType(.familyName)
                .autocapitalization(.words)
            }
            
            // Email Field
            TaskMateTextField(
                title: "Email",
                text: $email,
                placeholder: "<EMAIL>"
            )
            .focused($focusedField, equals: .email)
            .textContentType(.emailAddress)
            .keyboardType(.emailAddress)
            .autocapitalization(.none)
            .autocorrectionDisabled()
            
            // Password Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Password")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                HStack {
                    if showPassword {
                        TextField("Enter your password", text: $password)
                            .focused($focusedField, equals: .password)
                    } else {
                        SecureField("Enter your password", text: $password)
                            .focused($focusedField, equals: .password)
                    }
                    
                    Button(action: { showPassword.toggle() }) {
                        Image(systemName: showPassword ? "eye.slash" : "eye")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
                .padding()
                .background(DesignSystem.Colors.surface)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(focusedField == .password ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
                )
            }
            
            // Confirm Password Field
            VStack(alignment: .leading, spacing: 8) {
                Text("Confirm Password")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                
                HStack {
                    if showConfirmPassword {
                        TextField("Confirm your password", text: $confirmPassword)
                            .focused($focusedField, equals: .confirmPassword)
                    } else {
                        SecureField("Confirm your password", text: $confirmPassword)
                            .focused($focusedField, equals: .confirmPassword)
                    }
                    
                    Button(action: { showConfirmPassword.toggle() }) {
                        Image(systemName: showConfirmPassword ? "eye.slash" : "eye")
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                }
                .padding()
                .background(DesignSystem.Colors.surface)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(focusedField == .confirmPassword ? DesignSystem.Colors.primary : Color.clear, lineWidth: 2)
                )
                
                // Password validation feedback
                if !password.isEmpty {
                    passwordValidationView
                }
            }
        }
    }
    
    // MARK: - Password Validation
    private var passwordValidationView: some View {
        VStack(alignment: .leading, spacing: 4) {
            validationRow(
                text: "At least 6 characters",
                isValid: password.count >= 6
            )
            
            if !confirmPassword.isEmpty {
                validationRow(
                    text: "Passwords match",
                    isValid: password == confirmPassword
                )
            }
        }
        .padding(.top, 4)
    }
    
    private func validationRow(text: String, isValid: Bool) -> some View {
        HStack(spacing: 8) {
            Image(systemName: isValid ? "checkmark.circle.fill" : "xmark.circle.fill")
                .foregroundColor(isValid ? .green : .red)
                .font(.caption)
            
            Text(text)
                .font(DesignSystem.Typography.caption)
                .foregroundColor(isValid ? .green : .red)
            
            Spacer()
        }
    }
    
    // MARK: - Terms Section
    private var termsSection: some View {
        HStack(alignment: .top, spacing: 12) {
            Button(action: { agreedToTerms.toggle() }) {
                Image(systemName: agreedToTerms ? "checkmark.square.fill" : "square")
                    .foregroundColor(agreedToTerms ? DesignSystem.Colors.primary : DesignSystem.Colors.textSecondary)
                    .font(.title3)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text("I agree to the Terms of Service and Privacy Policy")
                    .font(DesignSystem.Typography.caption)
                    .foregroundColor(DesignSystem.Colors.text)
                
                HStack(spacing: 4) {
                    Button("Terms of Service") {
                        // Handle terms tap
                    }
                    .foregroundColor(DesignSystem.Colors.primary)
                    
                    Text("and")
                        .foregroundColor(DesignSystem.Colors.textSecondary)
                    
                    Button("Privacy Policy") {
                        // Handle privacy policy tap
                    }
                    .foregroundColor(DesignSystem.Colors.primary)
                }
                .font(DesignSystem.Typography.caption)
            }
            
            Spacer()
        }
    }
    
    // MARK: - Register Button
    private var registerButton: some View {
        TaskMateButton(
            title: authManager.isLoading ? "Creating Account..." : "Create Account",
            style: .primary,
            isLoading: authManager.isLoading
        ) {
            _Concurrency.Task {
                await handleRegistration()
            }
        }
        .disabled(!isFormValid || authManager.isLoading)
    }
    
    // MARK: - Login Link
    private var loginLink: some View {
        HStack(spacing: 4) {
            Text("Already have an account?")
                .font(DesignSystem.Typography.body)
                .foregroundColor(DesignSystem.Colors.textSecondary)
            
            Button("Sign In") {
                dismiss()
            }
            .font(DesignSystem.Typography.bodyBold)
            .foregroundColor(DesignSystem.Colors.primary)
        }
    }
    
    // MARK: - Form Validation
    private var isFormValid: Bool {
        !firstName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !lastName.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        !email.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty &&
        password.count >= 6 &&
        password == confirmPassword &&
        agreedToTerms
    }
    
    // MARK: - Actions
    private func handleRegistration() async {
        let success = await authManager.register(
            email: email.trimmingCharacters(in: .whitespacesAndNewlines),
            password: password,
            firstName: firstName.trimmingCharacters(in: .whitespacesAndNewlines),
            lastName: lastName.trimmingCharacters(in: .whitespacesAndNewlines)
        )
        
        if success {
            // Registration successful - AuthenticationManager will handle state update
            dismiss()
        }
    }
}

// MARK: - Preview
struct RegistrationView_Previews: PreviewProvider {
    static var previews: some View {
        RegistrationView()
    }
}
