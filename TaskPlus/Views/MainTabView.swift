//
//  MainTabView.swift
//  TaskPlus
//
//  Created by TaskMate Development Team on January 2025.
//

import SwiftUI

struct MainTabView: View {
    @State private var selectedTab = 0
    
    var body: some View {
        TabView(selection: $selectedTab) {
            // Home Dashboard
            HomeView()
                .tabItem {
                    Image(systemName: selectedTab == 0 ? "house.fill" : "house")
                    Text("Home")
                }
                .tag(0)
            
            // Personal Tasks
            PersonalTasksView()
                .tabItem {
                    Image(systemName: selectedTab == 1 ? "checkmark.square.fill" : "checkmark.square")
                    Text("Tasks")
                }
                .tag(1)
            
            // Groups
            GroupsView()
                .tabItem {
                    Image(systemName: selectedTab == 2 ? "person.3.fill" : "person.3")
                    Text("Groups")
                }
                .tag(2)
            
            // Friends
            FriendsView()
                .tabItem {
                    Image(systemName: selectedTab == 3 ? "heart.fill" : "heart")
                    Text("Friends")
                }
                .tag(3)
            
            // Settings
            SettingsView()
                .tabItem {
                    Image(systemName: selectedTab == 4 ? "gearshape.fill" : "gearshape")
                    Text("Settings")
                }
                .tag(4)
        }
        .accentColor(DesignSystem.Colors.sunsetCoral)
        .onAppear {
            setupTabBarAppearance()
        }
    }
    
    private func setupTabBarAppearance() {
        let appearance = UITabBarAppearance()
        appearance.configureWithOpaqueBackground()
        appearance.backgroundColor = UIColor.white
        
        // Selected item color
        appearance.selectionIndicatorTintColor = UIColor(DesignSystem.Colors.sunsetCoral)
        
        // Normal item color
        appearance.stackedLayoutAppearance.normal.iconColor = UIColor(DesignSystem.Colors.textSecondary)
        appearance.stackedLayoutAppearance.normal.titleTextAttributes = [
            .foregroundColor: UIColor(DesignSystem.Colors.textSecondary)
        ]
        
        // Selected item color
        appearance.stackedLayoutAppearance.selected.iconColor = UIColor(DesignSystem.Colors.sunsetCoral)
        appearance.stackedLayoutAppearance.selected.titleTextAttributes = [
            .foregroundColor: UIColor(DesignSystem.Colors.sunsetCoral)
        ]
        
        UITabBar.appearance().standardAppearance = appearance
        UITabBar.appearance().scrollEdgeAppearance = appearance
    }
}

// MARK: - Placeholder Views
struct HomeView: View {
    var body: some View {
        NavigationView {
            ZStack {
                // Gradient background
                LinearGradient(
                    colors: [
                        DesignSystem.Colors.sunriseOrange.opacity(0.1),
                        DesignSystem.Colors.dawnBlue.opacity(0.3)
                    ],
                    startPoint: .topLeading,
                    endPoint: .bottomTrailing
                )
                .ignoresSafeArea()
                
                VStack(spacing: DesignSystem.Spacing.lg) {
                    // Welcome message
                    VStack(spacing: DesignSystem.Spacing.sm) {
                        Text("Good Morning! 🌅")
                            .font(DesignSystem.Typography.displayMediumStyle)
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Text("Ready to make today productive?")
                            .font(DesignSystem.Typography.bodyLargeStyle)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                    }
                    
                    // Quick stats cards
                    HStack(spacing: DesignSystem.Spacing.md) {
                        QuickStatCard(title: "Today's Tasks", value: "5", color: DesignSystem.Colors.sunriseOrange)
                        QuickStatCard(title: "Completed", value: "2", color: DesignSystem.Colors.success)
                        QuickStatCard(title: "Streak", value: "7", color: DesignSystem.Colors.goldenHour)
                    }
                    
                    Spacer()
                    
                    // Coming soon message
                    VStack(spacing: DesignSystem.Spacing.md) {
                        Image(systemName: "sparkles")
                            .font(.system(size: 48))
                            .foregroundColor(DesignSystem.Colors.sunsetCoral)
                        
                        Text("Dashboard Coming Soon")
                            .font(DesignSystem.Typography.headlineLargeStyle)
                            .foregroundColor(DesignSystem.Colors.textPrimary)
                        
                        Text("We're building an amazing dashboard to help you track your progress and stay motivated!")
                            .font(DesignSystem.Typography.bodyMediumStyle)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, DesignSystem.Spacing.lg)
                    }
                    
                    Spacer()
                }
                .padding(DesignSystem.Spacing.lg)
            }
            .navigationTitle("TaskMate")
            .navigationBarTitleDisplayMode(.large)
        }
    }
}

struct QuickStatCard: View {
    let title: String
    let value: String
    let color: Color
    
    var body: some View {
        VStack(spacing: DesignSystem.Spacing.xs) {
            Text(value)
                .font(DesignSystem.Typography.displaySmallStyle)
                .foregroundColor(color)
            
            Text(title)
                .font(DesignSystem.Typography.captionMediumStyle)
                .foregroundColor(DesignSystem.Colors.textSecondary)
                .multilineTextAlignment(.center)
        }
        .frame(maxWidth: .infinity)
        .padding(DesignSystem.Spacing.md)
        .taskMateCard()
    }
}

struct PersonalTasksView: View {
    @EnvironmentObject private var dataManager: DataManager
    @State private var showingTaskCreation = false

    var body: some View {
        NavigationView {
            VStack {
                if dataManager.tasks.isEmpty {
                    // Empty State
                    VStack(spacing: 24) {
                        Spacer()

                        Image(systemName: "checkmark.square")
                            .font(.system(size: 64))
                            .foregroundColor(DesignSystem.Colors.primary)

                        Text("No Tasks Yet")
                            .font(DesignSystem.Typography.title1)
                            .foregroundColor(DesignSystem.Colors.text)

                        Text("Create your first task to get started with TaskMate!")
                            .font(DesignSystem.Typography.body)
                            .foregroundColor(DesignSystem.Colors.textSecondary)
                            .multilineTextAlignment(.center)
                            .padding(.horizontal, 32)

                        TaskMateButton("Create Your First Task") {
                            showingTaskCreation = true
                        }
                        .padding(.horizontal, 32)

                        Spacer()
                    }
                } else {
                    // Task List
                    List {
                        ForEach(dataManager.tasks) { task in
                            TaskRowView(task: task)
                        }
                    }
                    .listStyle(PlainListStyle())
                }
            }
            .navigationTitle("My Tasks")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(action: {
                        showingTaskCreation = true
                    }) {
                        Image(systemName: "plus")
                            .foregroundColor(DesignSystem.Colors.primary)
                    }
                }
            }
        }
        .sheet(isPresented: $showingTaskCreation) {
            EnhancedTaskCreationView()
        }
    }
}

struct GroupsView: View {
    var body: some View {
        NavigationView {
            PlaceholderView(
                icon: "person.3",
                title: "Groups",
                description: "Collaborate with teams, friends, and family. Create groups, assign tasks, and achieve goals together!"
            )
            .navigationTitle("Groups")
        }
    }
}

struct FriendsView: View {
    var body: some View {
        NavigationView {
            PlaceholderView(
                icon: "heart",
                title: "Friends",
                description: "Connect with friends, send motivational messages, and celebrate achievements together!"
            )
            .navigationTitle("Friends")
        }
    }
}

struct SettingsView: View {
    var body: some View {
        NavigationView {
            PlaceholderView(
                icon: "gearshape",
                title: "Settings",
                description: "Customize your TaskMate experience, manage your profile, and adjust your preferences."
            )
            .navigationTitle("Settings")
        }
    }
}

struct PlaceholderView: View {
    let icon: String
    let title: String
    let description: String
    
    var body: some View {
        ZStack {
            // Gradient background
            LinearGradient(
                colors: [
                    DesignSystem.Colors.twilightPurple.opacity(0.3),
                    DesignSystem.Colors.dawnBlue.opacity(0.2)
                ],
                startPoint: .topLeading,
                endPoint: .bottomTrailing
            )
            .ignoresSafeArea()
            
            VStack(spacing: DesignSystem.Spacing.lg) {
                Spacer()
                
                Image(systemName: icon)
                    .font(.system(size: 64))
                    .foregroundColor(DesignSystem.Colors.sunsetCoral)
                
                Text(title)
                    .font(DesignSystem.Typography.displaySmallStyle)
                    .foregroundColor(DesignSystem.Colors.textPrimary)
                
                Text(description)
                    .font(DesignSystem.Typography.bodyMediumStyle)
                    .foregroundColor(DesignSystem.Colors.textSecondary)
                    .multilineTextAlignment(.center)
                    .padding(.horizontal, DesignSystem.Spacing.lg)
                
                Text("Coming Soon! 🚀")
                    .font(DesignSystem.Typography.headlineSmallStyle)
                    .foregroundColor(DesignSystem.Colors.goldenHour)
                    .padding(.top, DesignSystem.Spacing.md)
                
                Spacer()
            }
            .padding(DesignSystem.Spacing.lg)
        }
    }
}

#Preview {
    MainTabView()
}
