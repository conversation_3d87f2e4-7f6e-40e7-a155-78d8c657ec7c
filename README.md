# TaskMate - Social Task Management iOS App

<div align="center">
  <img src="https://img.shields.io/badge/iOS-18.2+-blue.svg" alt="iOS Version">
  <img src="https://img.shields.io/badge/Swift-5.0+-orange.svg" alt="Swift Version">
  <img src="https://img.shields.io/badge/SwiftUI-✓-green.svg" alt="SwiftUI">
  <img src="https://img.shields.io/badge/Status-Phase%201%20Complete-success.svg" alt="Status">
</div>

## 🌅 Overview

TaskMate is a beautiful, social task management application for iOS that combines personal productivity with collaborative features. Built with SwiftUI and featuring a warm sunrise/sunset design theme, TaskMate helps users manage their tasks while staying connected with friends and groups.

## ✨ Features

### 🏗️ Phase 1 - Foundation (COMPLETED)
- ✅ **Core Architecture**: Clean, modular project structure
- ✅ **Data Models**: User, Task, and Group entities with relationships
- ✅ **Design System**: Sunrise/sunset color palette with consistent typography
- ✅ **UI Components**: Reusable TaskMateButton and TaskMateTextField
- ✅ **Services Layer**: DataManager, NotificationManager, and SettingsManager
- ✅ **Navigation**: Main tab view structure

### 🔮 Upcoming Features
- 🔐 **Authentication**: User registration and login
- 📝 **Task Management**: Create, edit, complete, and organize tasks
- 👥 **Group Collaboration**: Create groups and assign tasks to members
- 👫 **Social Features**: Friend connections and motivational messaging
- 📊 **Analytics**: Personal and group productivity insights
- 🔔 **Notifications**: Smart reminders and real-time updates

## 🎨 Design Philosophy

TaskMate embraces a **warm, welcoming aesthetic** inspired by sunrise and sunset colors:
- **Primary Colors**: Sunrise Orange (#FF6B35) and Sunset Coral (#FF8E53)
- **Typography**: Clean, accessible font hierarchy
- **User Experience**: Intuitive navigation with satisfying interactions

## 🏗️ Architecture

### Project Structure
```
TaskPlus/
├── Models/
│   ├── User.swift          # User data model with stats and profile
│   ├── Task.swift          # Task model with priority and status
│   └── Group.swift         # Group model for collaboration
├── Views/
│   ├── Components/
│   │   ├── TaskMateButton.swift    # Reusable button component
│   │   └── TaskMateTextField.swift # Reusable text field
│   ├── MainTabView.swift   # Main navigation structure
│   └── ContentView.swift   # Root view
├── Services/
│   ├── DataManager.swift   # Core data management
│   ├── NotificationManager.swift # Push notifications
│   └── SettingsManager.swift     # App settings
├── Utils/
│   └── DesignSystem.swift  # Colors, fonts, and styling
└── TaskPlusApp.swift       # App entry point
```

### Key Components

#### DataManager
- Centralized data management with ObservableObject
- Sample data for development and testing
- CRUD operations for tasks, groups, and users
- Search and filtering capabilities

#### NotificationManager
- Local and push notification support
- Task reminders and due date alerts
- Group activity notifications
- Customizable notification preferences

#### SettingsManager
- User preferences and app configuration
- Theme management (multiple color schemes)
- Language and localization settings
- Privacy and accessibility options

## 🚀 Getting Started

### Prerequisites
- Xcode 15.0+
- iOS 18.2+ deployment target
- macOS 14.0+ for development

### Installation
1. Clone the repository:
   ```bash
   git clone https://github.com/yourusername/TaskMate.git
   cd TaskMate
   ```

2. Open the project in Xcode:
   ```bash
   open TaskPlus.xcodeproj
   ```

3. Build and run:
   - Select your target device or simulator
   - Press `Cmd + R` to build and run

### Build Status
✅ **BUILD SUCCEEDED** - All components compile successfully

## 🧪 Testing

The project includes comprehensive testing infrastructure:
- Unit tests for data models
- Service layer testing
- Component integration tests
- Performance testing for search and filtering

Run tests with:
```bash
xcodebuild test -project TaskPlus.xcodeproj -scheme TaskPlus -destination 'platform=iOS Simulator,name=iPhone 16'
```

## 📱 Supported Devices

- iPhone (iOS 18.2+)
- iPad (iOS 18.2+)
- Optimized for all screen sizes

## 🎯 Development Roadmap

### Current Status: Phase 1 Complete ✅

### Next Phase: Authentication & User Management
- User registration and login system
- Profile creation and management
- Session persistence
- Privacy settings

See [DEVELOPMENT_PLAN.md](DEVELOPMENT_PLAN.md) for detailed roadmap.

## 🤝 Contributing

We welcome contributions! Please see our contributing guidelines:

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow Swift style guidelines
- Write comprehensive tests for new features
- Update documentation for API changes
- Ensure accessibility compliance

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- SwiftUI community for inspiration and best practices
- Design inspiration from sunrise/sunset photography
- iOS development community for guidance and support

## 📞 Contact

- **Project Lead**: Mohammad Alshammari
- **Email**: [<EMAIL>]
- **GitHub**: [@yourusername]

---

<div align="center">
  <p>Made with ❤️ and ☀️ by the TaskMate Team</p>
  <p>Building better productivity, one task at a time.</p>
</div>
